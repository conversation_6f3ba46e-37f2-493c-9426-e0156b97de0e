"""
QuantumForex Pro 启动脚本
快速启动世界顶级量化交易系统
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False

    print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")

    required_packages = [
        'pandas', 'numpy', 'sklearn',
        'pymysql', 'requests', 'psutil', 'scipy'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)

    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False

    print("✅ 所有依赖包检查通过")
    return True

def check_system_resources():
    """检查系统资源"""
    print("🖥️ 检查系统资源...")

    try:
        import psutil

        # 检查内存
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        print(f"💾 总内存: {memory_gb:.1f} GB")
        print(f"💾 可用内存: {memory.available / (1024**3):.1f} GB")

        if memory.available < 1 * (1024**3):  # 少于1GB可用内存
            print("⚠️ 可用内存较少，系统可能运行缓慢")

        # 检查CPU
        cpu_count = psutil.cpu_count()
        print(f"🖥️ CPU核心数: {cpu_count}")

        # 检查磁盘空间
        disk = psutil.disk_usage('.')
        disk_free_gb = disk.free / (1024**3)
        print(f"💽 可用磁盘空间: {disk_free_gb:.1f} GB")

        if disk_free_gb < 1:
            print("⚠️ 磁盘空间不足")
            return False

        print("✅ 系统资源检查通过")
        return True

    except ImportError:
        print("⚠️ 无法检查系统资源 (psutil未安装)")
        return True

def create_directories():
    """创建必要的目录"""
    print("📁 创建目录结构...")

    directories = [
        'data',
        'data/models',
        'data/cache',
        'data/logs',
        'strategies',
        'utils',
        'tests'
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ {directory}/")

    print("✅ 目录结构创建完成")

def display_system_info():
    """显示系统信息"""
    print("\n" + "="*60)
    print("🏆 QuantumForex Pro - 世界顶级量化交易系统")
    print("="*60)
    print(f"🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🖥️ 操作系统: {os.name}")
    print(f"🐍 Python版本: {sys.version.split()[0]}")
    print(f"📂 工作目录: {os.getcwd()}")
    print("="*60)

def main():
    """主启动函数"""
    try:
        print("🚀 QuantumForex Pro 启动检查...")
        print("-" * 40)

        # 1. 检查Python版本
        if not check_python_version():
            return False

        # 2. 创建目录结构
        create_directories()

        # 3. 检查系统资源
        if not check_system_resources():
            print("⚠️ 系统资源不足，但将尝试继续运行...")

        # 4. 检查依赖包
        if not check_dependencies():
            print("\n💡 安装依赖包:")
            print("pip install -r requirements.txt")
            return False

        # 5. 显示系统信息
        display_system_info()

        # 6. 启动主程序
        print("🎯 启动 QuantumForex Pro 主程序...")
        print("按 Ctrl+C 停止系统\n")

        # 导入并启动主程序
        from main import main as main_program
        main_program()

        return True

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断启动")
        return False
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ QuantumForex Pro 启动失败")
        input("按回车键退出...")
        sys.exit(1)
