"""
QuantumForex Pro - 高级风险管理引擎
多层次风险控制，确保稳定收益和低回撤
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import json

from config.config import config

class RiskLevel(Enum):
    """风险等级枚举"""
    VERY_LOW = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    VERY_HIGH = 5

class RiskAction(Enum):
    """风险行动枚举"""
    ALLOW_TRADING = "allow_trading"
    REDUCE_POSITION = "reduce_position"
    STOP_NEW_TRADES = "stop_new_trades"
    CLOSE_ALL_POSITIONS = "close_all_positions"
    EMERGENCY_STOP = "emergency_stop"

@dataclass
class RiskMetrics:
    """风险指标数据结构"""
    account_drawdown: float
    daily_pnl: float
    weekly_pnl: float
    monthly_pnl: float
    position_risk: float
    portfolio_risk: float
    volatility_risk: float
    correlation_risk: float
    var_95: float  # 95% VaR
    risk_level: RiskLevel
    recommended_action: RiskAction
    risk_score: float
    timestamp: str

@dataclass
class PositionRisk:
    """单个持仓风险"""
    symbol: str
    position_size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    risk_amount: float
    risk_percentage: float
    stop_loss: float
    take_profit: float
    holding_time: float  # 小时
    risk_level: RiskLevel

class AdvancedRiskManager:
    """
    高级风险管理引擎
    实现多层次、动态的风险控制
    """

    def __init__(self):
        self.config = config.RISK_CONFIG
        self.performance_targets = config.PERFORMANCE_TARGETS

        # 风险状态跟踪
        self.daily_stats = {
            'pnl': 0.0,
            'trades_count': 0,
            'consecutive_losses': 0,
            'max_drawdown_today': 0.0,
            'last_reset': datetime.now().date()
        }

        self.weekly_stats = {
            'pnl': 0.0,
            'trades_count': 0,
            'max_drawdown_week': 0.0,
            'last_reset': datetime.now().isocalendar()[1]
        }

        self.monthly_stats = {
            'pnl': 0.0,
            'trades_count': 0,
            'max_drawdown_month': 0.0,
            'last_reset': datetime.now().month
        }

        # 风险历史记录
        self.risk_history = []
        self.correlation_matrix = {}

    def assess_comprehensive_risk(self, account_info: Dict, positions: List[Dict],
                                market_data: Dict) -> RiskMetrics:
        """
        综合风险评估

        Args:
            account_info: 账户信息
            positions: 当前持仓列表
            market_data: 市场数据

        Returns:
            RiskMetrics: 风险评估结果
        """
        try:
            # 重置统计数据（如果需要）
            self._reset_stats_if_needed()

            # 1. 账户级风险评估
            account_risk = self._assess_account_risk(account_info)

            # 2. 持仓级风险评估
            position_risks = self._assess_position_risks(positions, account_info)

            # 3. 组合级风险评估
            portfolio_risk = self._assess_portfolio_risk(position_risks, market_data)

            # 4. 市场风险评估
            market_risk = self._assess_market_risk(market_data)

            # 5. 计算VaR
            var_95 = self._calculate_var(positions, market_data)

            # 6. 综合风险评分
            risk_score = self._calculate_comprehensive_risk_score(
                account_risk, portfolio_risk, market_risk
            )

            # 7. 确定风险等级
            risk_level = self._determine_risk_level(risk_score)

            # 8. 推荐风险行动
            recommended_action = self._determine_risk_action(risk_level, account_risk, portfolio_risk)

            # 创建风险指标
            risk_metrics = RiskMetrics(
                account_drawdown=account_risk['drawdown'],
                daily_pnl=self.daily_stats['pnl'],
                weekly_pnl=self.weekly_stats['pnl'],
                monthly_pnl=self.monthly_stats['pnl'],
                position_risk=portfolio_risk['total_position_risk'],
                portfolio_risk=portfolio_risk['portfolio_risk'],
                volatility_risk=market_risk['volatility_risk'],
                correlation_risk=portfolio_risk['correlation_risk'],
                var_95=var_95,
                risk_level=risk_level,
                recommended_action=recommended_action,
                risk_score=risk_score,
                timestamp=datetime.now().isoformat()
            )

            # 记录风险历史
            self._record_risk_history(risk_metrics)

            return risk_metrics

        except Exception as e:
            print(f"❌ 风险评估失败: {e}")
            return self._create_default_risk_metrics()

    def _assess_account_risk(self, account_info: Dict) -> Dict:
        """评估账户级风险"""
        try:
            balance = account_info.get('balance', 10000)
            equity = account_info.get('equity', balance)
            initial_balance = account_info.get('initial_balance', balance)

            # 计算回撤
            drawdown = max(0, (initial_balance - equity) / initial_balance)

            # 计算今日PnL
            daily_pnl = account_info.get('daily_pnl', 0)
            daily_pnl_percentage = daily_pnl / balance if balance > 0 else 0

            # 更新统计
            self.daily_stats['pnl'] = daily_pnl_percentage
            self.daily_stats['max_drawdown_today'] = max(
                self.daily_stats['max_drawdown_today'], drawdown
            )

            return {
                'balance': balance,
                'equity': equity,
                'drawdown': drawdown,
                'daily_pnl': daily_pnl,
                'daily_pnl_percentage': daily_pnl_percentage,
                'margin_used': account_info.get('margin_used', 0),
                'margin_free': account_info.get('margin_free', balance),
                'margin_level': account_info.get('margin_level', 1000)
            }

        except Exception as e:
            print(f"❌ 账户风险评估失败: {e}")
            return {'drawdown': 0, 'daily_pnl_percentage': 0}

    def _assess_position_risks(self, positions: List[Dict], account_info: Dict) -> List[PositionRisk]:
        """评估持仓风险"""
        position_risks = []

        try:
            balance = account_info.get('balance', 10000)

            for position in positions:
                symbol = position.get('symbol', 'UNKNOWN')
                position_size = position.get('lots', 0)
                entry_price = position.get('open_price', 0)
                current_price = position.get('current_price', entry_price)
                unrealized_pnl = position.get('profit', 0)

                # 计算风险金额
                stop_loss = position.get('stop_loss', 0)
                if stop_loss > 0:
                    risk_amount = abs((entry_price - stop_loss) * position_size * 100000)  # 假设标准手
                else:
                    risk_amount = abs(unrealized_pnl) if unrealized_pnl < 0 else 0

                risk_percentage = risk_amount / balance if balance > 0 else 0

                # 计算持仓时间
                open_time = position.get('open_time', datetime.now())
                if isinstance(open_time, str):
                    open_time = datetime.fromisoformat(open_time)
                holding_time = (datetime.now() - open_time).total_seconds() / 3600

                # 确定风险等级
                if risk_percentage > 0.05:  # 5%
                    risk_level = RiskLevel.VERY_HIGH
                elif risk_percentage > 0.03:  # 3%
                    risk_level = RiskLevel.HIGH
                elif risk_percentage > 0.02:  # 2%
                    risk_level = RiskLevel.MEDIUM
                elif risk_percentage > 0.01:  # 1%
                    risk_level = RiskLevel.LOW
                else:
                    risk_level = RiskLevel.VERY_LOW

                position_risk = PositionRisk(
                    symbol=symbol,
                    position_size=position_size,
                    entry_price=entry_price,
                    current_price=current_price,
                    unrealized_pnl=unrealized_pnl,
                    risk_amount=risk_amount,
                    risk_percentage=risk_percentage,
                    stop_loss=stop_loss,
                    take_profit=position.get('take_profit', 0),
                    holding_time=holding_time,
                    risk_level=risk_level
                )

                position_risks.append(position_risk)

            return position_risks

        except Exception as e:
            print(f"❌ 持仓风险评估失败: {e}")
            return []

    def _assess_portfolio_risk(self, position_risks: List[PositionRisk], market_data: Dict) -> Dict:
        """评估组合风险"""
        try:
            if not position_risks:
                return {
                    'total_position_risk': 0,
                    'portfolio_risk': 0,
                    'correlation_risk': 0,
                    'concentration_risk': 0
                }

            # 计算总持仓风险
            total_position_risk = sum(pos.risk_percentage for pos in position_risks)

            # 计算相关性风险
            correlation_risk = self._calculate_correlation_risk(position_risks)

            # 计算集中度风险
            concentration_risk = self._calculate_concentration_risk(position_risks)

            # 综合组合风险
            portfolio_risk = (
                total_position_risk * 0.5 +
                correlation_risk * 0.3 +
                concentration_risk * 0.2
            )

            return {
                'total_position_risk': total_position_risk,
                'portfolio_risk': portfolio_risk,
                'correlation_risk': correlation_risk,
                'concentration_risk': concentration_risk,
                'position_count': len(position_risks),
                'avg_position_risk': total_position_risk / len(position_risks) if position_risks else 0
            }

        except Exception as e:
            print(f"❌ 组合风险评估失败: {e}")
            return {'total_position_risk': 0, 'portfolio_risk': 0, 'correlation_risk': 0}

    def _assess_market_risk(self, market_data: Dict) -> Dict:
        """评估市场风险"""
        try:
            # 获取市场波动率
            volatility = market_data.get('volatility', 0.01)

            # 计算波动率风险
            normal_volatility = 0.01  # 正常波动率1%
            volatility_risk = min(volatility / normal_volatility, 3.0)  # 最大3倍

            # 获取市场情绪
            market_sentiment = market_data.get('sentiment', 'NEUTRAL')
            sentiment_risk = {
                'VERY_BULLISH': 0.2,
                'BULLISH': 0.1,
                'NEUTRAL': 0.0,
                'BEARISH': 0.1,
                'VERY_BEARISH': 0.2
            }.get(market_sentiment, 0.0)

            # 流动性风险
            liquidity_risk = market_data.get('liquidity_risk', 0.0)

            return {
                'volatility_risk': volatility_risk,
                'sentiment_risk': sentiment_risk,
                'liquidity_risk': liquidity_risk,
                'overall_market_risk': (volatility_risk + sentiment_risk + liquidity_risk) / 3
            }

        except Exception as e:
            print(f"❌ 市场风险评估失败: {e}")
            return {'volatility_risk': 1.0, 'sentiment_risk': 0.0, 'liquidity_risk': 0.0}

    def _calculate_correlation_risk(self, position_risks: List[PositionRisk]) -> float:
        """计算相关性风险"""
        try:
            if len(position_risks) < 2:
                return 0.0

            # 简化的相关性计算
            # 基于货币对的相关性
            currency_pairs = [pos.symbol for pos in position_risks]

            # 预定义的货币对相关性
            high_correlation_pairs = [
                ('EURUSD', 'GBPUSD'),
                ('AUDUSD', 'NZDUSD'),
                ('USDCHF', 'EURUSD'),  # 负相关
                ('USDCAD', 'USDCHF')
            ]

            correlation_risk = 0.0
            for i, pair1 in enumerate(currency_pairs):
                for j, pair2 in enumerate(currency_pairs[i+1:], i+1):
                    # 检查是否为高相关性货币对
                    if (pair1, pair2) in high_correlation_pairs or (pair2, pair1) in high_correlation_pairs:
                        # 计算相关性风险
                        risk1 = position_risks[i].risk_percentage
                        risk2 = position_risks[j].risk_percentage
                        correlation_risk += risk1 * risk2 * 0.8  # 假设相关性为0.8

            return min(correlation_risk, 0.5)  # 最大50%相关性风险

        except Exception:
            return 0.0

    def _calculate_concentration_risk(self, position_risks: List[PositionRisk]) -> float:
        """计算集中度风险"""
        try:
            if not position_risks:
                return 0.0

            # 计算最大单一持仓风险
            max_position_risk = max(pos.risk_percentage for pos in position_risks)

            # 计算风险分布的基尼系数（简化版）
            risks = [pos.risk_percentage for pos in position_risks]
            risks.sort()
            n = len(risks)

            if n == 1:
                concentration_risk = max_position_risk
            else:
                # 简化的集中度计算
                total_risk = sum(risks)
                if total_risk > 0:
                    concentration_risk = max_position_risk / total_risk
                else:
                    concentration_risk = 0

            return min(concentration_risk, 1.0)

        except Exception:
            return 0.0

    def _calculate_var(self, positions: List[Dict], market_data: Dict) -> float:
        """计算95% VaR (Value at Risk)"""
        try:
            if not positions:
                return 0.0

            # 简化的VaR计算
            total_exposure = 0
            for position in positions:
                position_size = position.get('lots', 0)
                current_price = position.get('current_price', 1.0)
                total_exposure += abs(position_size * current_price * 100000)  # 标准手

            # 获取市场波动率
            volatility = market_data.get('volatility', 0.01)

            # 95% VaR = 1.645 * 标准差 * 暴露金额
            var_95 = 1.645 * volatility * total_exposure

            return var_95

        except Exception:
            return 0.0

    def _calculate_comprehensive_risk_score(self, account_risk: Dict, portfolio_risk: Dict,
                                          market_risk: Dict) -> float:
        """计算综合风险评分"""
        try:
            # 权重分配
            weights = {
                'account': 0.4,
                'portfolio': 0.4,
                'market': 0.2
            }

            # 账户风险评分
            account_score = (
                account_risk.get('drawdown', 0) * 2 +  # 回撤权重更高
                abs(account_risk.get('daily_pnl_percentage', 0)) * 1
            )

            # 组合风险评分
            portfolio_score = portfolio_risk.get('portfolio_risk', 0)

            # 市场风险评分
            market_score = market_risk.get('overall_market_risk', 0)

            # 综合评分
            total_score = (
                account_score * weights['account'] +
                portfolio_score * weights['portfolio'] +
                market_score * weights['market']
            )

            return min(total_score, 5.0)  # 限制最大评分为5

        except Exception:
            return 2.5  # 默认中等风险

    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """确定风险等级"""
        if risk_score < 1.0:
            return RiskLevel.VERY_LOW
        elif risk_score < 2.0:
            return RiskLevel.LOW
        elif risk_score < 3.0:
            return RiskLevel.MEDIUM
        elif risk_score < 4.0:
            return RiskLevel.HIGH
        else:
            return RiskLevel.VERY_HIGH

    def _determine_risk_action(self, risk_level: RiskLevel, account_risk: Dict,
                             portfolio_risk: Dict) -> RiskAction:
        """确定风险行动"""
        try:
            # 紧急情况检查
            if account_risk.get('drawdown', 0) > self.config['account_risk']['max_drawdown']:
                return RiskAction.EMERGENCY_STOP

            if account_risk.get('daily_pnl_percentage', 0) < -self.config['account_risk']['daily_loss_limit']:
                return RiskAction.CLOSE_ALL_POSITIONS

            # 基于风险等级决定
            if risk_level == RiskLevel.VERY_HIGH:
                return RiskAction.CLOSE_ALL_POSITIONS
            elif risk_level == RiskLevel.HIGH:
                return RiskAction.STOP_NEW_TRADES
            elif risk_level == RiskLevel.MEDIUM:
                if portfolio_risk.get('total_position_risk', 0) > 0.15:
                    return RiskAction.REDUCE_POSITION
                else:
                    return RiskAction.ALLOW_TRADING
            else:
                return RiskAction.ALLOW_TRADING

        except Exception:
            return RiskAction.ALLOW_TRADING

    def _reset_stats_if_needed(self):
        """重置统计数据（如果需要）"""
        try:
            current_date = datetime.now().date()
            current_week = datetime.now().isocalendar()[1]
            current_month = datetime.now().month

            # 重置日统计
            if self.daily_stats['last_reset'] != current_date:
                self.daily_stats = {
                    'pnl': 0.0,
                    'trades_count': 0,
                    'consecutive_losses': 0,
                    'max_drawdown_today': 0.0,
                    'last_reset': current_date
                }

            # 重置周统计
            if self.weekly_stats['last_reset'] != current_week:
                self.weekly_stats = {
                    'pnl': 0.0,
                    'trades_count': 0,
                    'max_drawdown_week': 0.0,
                    'last_reset': current_week
                }

            # 重置月统计
            if self.monthly_stats['last_reset'] != current_month:
                self.monthly_stats = {
                    'pnl': 0.0,
                    'trades_count': 0,
                    'max_drawdown_month': 0.0,
                    'last_reset': current_month
                }

        except Exception:
            pass

    def _create_default_risk_metrics(self) -> RiskMetrics:
        """创建默认风险指标"""
        return RiskMetrics(
            account_drawdown=0.0,
            daily_pnl=0.0,
            weekly_pnl=0.0,
            monthly_pnl=0.0,
            position_risk=0.0,
            portfolio_risk=0.0,
            volatility_risk=1.0,
            correlation_risk=0.0,
            var_95=0.0,
            risk_level=RiskLevel.LOW,
            recommended_action=RiskAction.ALLOW_TRADING,
            risk_score=1.0,
            timestamp=datetime.now().isoformat()
        )

    def _record_risk_history(self, risk_metrics: RiskMetrics):
        """记录风险历史"""
        try:
            history_record = {
                'timestamp': risk_metrics.timestamp,
                'risk_level': risk_metrics.risk_level.name,
                'risk_score': risk_metrics.risk_score,
                'account_drawdown': risk_metrics.account_drawdown,
                'recommended_action': risk_metrics.recommended_action.value
            }

            self.risk_history.append(history_record)

            # 保持历史记录在合理范围内
            if len(self.risk_history) > 1000:
                self.risk_history = self.risk_history[-500:]

        except Exception:
            pass
