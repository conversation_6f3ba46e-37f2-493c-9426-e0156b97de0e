"""
测试TradeResultRecorder的数据获取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.learning_system.trade_result_recorder import TradeResultRecorder

def test_trade_recorder():
    print("🧪 测试TradeResultRecorder数据获取功能")
    print("=" * 60)

    try:
        # 初始化记录器
        recorder = TradeResultRecorder()
        print("✅ TradeResultRecorder初始化成功")

        # 测试不同天数的数据获取
        test_days = [1, 7, 30, 90]

        for days in test_days:
            print(f"\n📊 测试获取最近{days}天的交易记录:")
            trades = recorder.get_recent_trades(days=days)
            print(f"   获取到 {len(trades)} 笔交易记录")

            if trades:
                print(f"   最早记录: {trades[-1].entry_time}")
                print(f"   最新记录: {trades[0].entry_time}")
                print(f"   示例交易: {trades[0].symbol} {trades[0].action} - 盈亏: ${trades[0].profit_loss:.2f}")

        # 测试统计功能
        print(f"\n📈 测试统计功能:")
        stats = recorder.get_statistics(days=30)
        print(f"   统计结果: {stats}")

        # 测试PatternAnalyzer会调用的方法
        print(f"\n🔍 测试PatternAnalyzer调用:")
        from core.learning_system.pattern_analyzer import PatternAnalyzer
        import logging

        # 设置日志级别为DEBUG以查看详细信息
        logging.basicConfig(level=logging.DEBUG)

        analyzer = PatternAnalyzer(recorder)

        # 直接测试获取交易数据
        print("   直接测试获取交易数据...")
        trades = recorder.get_recent_trades(days=90)
        print(f"   获取到 {len(trades)} 笔交易记录")

        # 测试数据转换
        if trades:
            print("   测试数据转换...")
            df = analyzer._trades_to_dataframe(trades)
            print(f"   转换后DataFrame形状: {df.shape}")
            print(f"   DataFrame列: {list(df.columns)}")
            if len(df) > 0:
                print(f"   示例数据: {df.iloc[0].to_dict()}")

        # 直接调用analyze_patterns看看会发生什么
        print("   调用analyze_patterns(days=90)...")
        patterns = analyzer.analyze_patterns(days=90)
        print(f"   返回结果: {patterns}")

        # 测试ParameterOptimizer
        print(f"\n⚙️ 测试ParameterOptimizer调用:")
        from core.learning_system.parameter_optimizer import ParameterOptimizer
        optimizer = ParameterOptimizer(recorder, analyzer)

        print("   调用optimize_parameters(force=True)...")
        try:
            results = optimizer.optimize_parameters(force=True)
            print(f"   优化结果: {len(results)} 个结果")
        except Exception as e:
            print(f"   优化失败: {e}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_trade_recorder()
