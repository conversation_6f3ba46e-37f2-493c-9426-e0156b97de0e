#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交易结果记录器
记录每笔交易的完整信息，为学习系统提供数据基础
"""

import json
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

@dataclass
class TradeRecord:
    """交易记录"""
    trade_id: str
    symbol: str
    action: str  # BUY/SELL
    entry_time: datetime
    entry_price: float
    volume: float
    
    # 策略参数（开仓时）
    strategy_used: str
    stop_loss: float
    take_profit: float
    confidence: float
    market_condition: str
    
    # 技术指标（开仓时）
    rsi: float
    ma_20: float
    ma_50: float
    atr: float
    volatility: float
    
    # 交易结果（平仓时）
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    exit_reason: Optional[str] = None  # stop_loss/take_profit/manual/timeout
    profit_loss: Optional[float] = None
    profit_loss_pct: Optional[float] = None
    holding_duration_minutes: Optional[int] = None
    
    # 市场环境（平仓时）
    market_change_during_trade: Optional[float] = None
    max_favorable_excursion: Optional[float] = None  # 最大有利变动
    max_adverse_excursion: Optional[float] = None    # 最大不利变动

class TradeResultRecorder:
    """交易结果记录器"""
    
    def __init__(self, db_path: str = "data/trade_results.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库
        self._init_database()
        
        # 内存缓存
        self.active_trades = {}  # trade_id -> TradeRecord
        
    def _init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS trade_records (
                        trade_id TEXT PRIMARY KEY,
                        symbol TEXT NOT NULL,
                        action TEXT NOT NULL,
                        entry_time TEXT NOT NULL,
                        entry_price REAL NOT NULL,
                        volume REAL NOT NULL,
                        strategy_used TEXT NOT NULL,
                        stop_loss REAL NOT NULL,
                        take_profit REAL NOT NULL,
                        confidence REAL NOT NULL,
                        market_condition TEXT NOT NULL,
                        rsi REAL NOT NULL,
                        ma_20 REAL NOT NULL,
                        ma_50 REAL NOT NULL,
                        atr REAL NOT NULL,
                        volatility REAL NOT NULL,
                        exit_time TEXT,
                        exit_price REAL,
                        exit_reason TEXT,
                        profit_loss REAL,
                        profit_loss_pct REAL,
                        holding_duration_minutes INTEGER,
                        market_change_during_trade REAL,
                        max_favorable_excursion REAL,
                        max_adverse_excursion REAL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建索引
                conn.execute("CREATE INDEX IF NOT EXISTS idx_symbol ON trade_records(symbol)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_entry_time ON trade_records(entry_time)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_strategy ON trade_records(strategy_used)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_exit_reason ON trade_records(exit_reason)")
                
                conn.commit()
                self.logger.info("交易记录数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def record_trade_entry(self, trade_data: Dict) -> str:
        """记录交易开仓"""
        try:
            # 生成交易ID
            trade_id = f"{trade_data['symbol']}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            # 创建交易记录
            trade_record = TradeRecord(
                trade_id=trade_id,
                symbol=trade_data['symbol'],
                action=trade_data['action'],
                entry_time=datetime.now(),
                entry_price=trade_data['entry_price'],
                volume=trade_data['volume'],
                strategy_used=trade_data.get('strategy_used', 'unknown'),
                stop_loss=trade_data['stop_loss'],
                take_profit=trade_data['take_profit'],
                confidence=trade_data.get('confidence', 0.5),
                market_condition=trade_data.get('market_condition', 'unknown'),
                rsi=trade_data.get('rsi', 50.0),
                ma_20=trade_data.get('ma_20', trade_data['entry_price']),
                ma_50=trade_data.get('ma_50', trade_data['entry_price']),
                atr=trade_data.get('atr', 0.002),
                volatility=trade_data.get('volatility', 0.01)
            )
            
            # 保存到内存缓存
            self.active_trades[trade_id] = trade_record
            
            # 保存到数据库
            self._save_to_database(trade_record)
            
            self.logger.info(f"记录交易开仓: {trade_id} - {trade_data['symbol']} {trade_data['action']}")
            return trade_id
            
        except Exception as e:
            self.logger.error(f"记录交易开仓失败: {e}")
            return ""
    
    def record_trade_exit(self, trade_id: str, exit_data: Dict):
        """记录交易平仓"""
        try:
            if trade_id not in self.active_trades:
                # 尝试从数据库加载
                trade_record = self._load_from_database(trade_id)
                if not trade_record:
                    self.logger.warning(f"未找到交易记录: {trade_id}")
                    return
            else:
                trade_record = self.active_trades[trade_id]
            
            # 更新平仓信息
            trade_record.exit_time = datetime.now()
            trade_record.exit_price = exit_data['exit_price']
            trade_record.exit_reason = exit_data.get('exit_reason', 'unknown')
            
            # 计算盈亏
            if trade_record.action == 'BUY':
                profit_loss = (exit_data['exit_price'] - trade_record.entry_price) * trade_record.volume
            else:  # SELL
                profit_loss = (trade_record.entry_price - exit_data['exit_price']) * trade_record.volume
            
            trade_record.profit_loss = profit_loss
            trade_record.profit_loss_pct = (profit_loss / (trade_record.entry_price * trade_record.volume)) * 100
            
            # 计算持仓时间
            duration = trade_record.exit_time - trade_record.entry_time
            trade_record.holding_duration_minutes = int(duration.total_seconds() / 60)
            
            # 计算市场变动
            market_change = (exit_data['exit_price'] - trade_record.entry_price) / trade_record.entry_price
            trade_record.market_change_during_trade = market_change
            
            # 计算最大有利/不利变动（如果提供）
            trade_record.max_favorable_excursion = exit_data.get('max_favorable_excursion', 0.0)
            trade_record.max_adverse_excursion = exit_data.get('max_adverse_excursion', 0.0)
            
            # 更新数据库
            self._update_database(trade_record)
            
            # 从活跃交易中移除
            if trade_id in self.active_trades:
                del self.active_trades[trade_id]
            
            self.logger.info(f"记录交易平仓: {trade_id} - 盈亏: ${profit_loss:.2f}")
            
        except Exception as e:
            self.logger.error(f"记录交易平仓失败: {e}")
    
    def _save_to_database(self, trade_record: TradeRecord):
        """保存到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 转换为字典
                data = asdict(trade_record)
                
                # 转换datetime为字符串
                data['entry_time'] = trade_record.entry_time.isoformat()
                if trade_record.exit_time:
                    data['exit_time'] = trade_record.exit_time.isoformat()
                
                # 插入数据
                columns = ', '.join(data.keys())
                placeholders = ', '.join(['?' for _ in data])
                
                conn.execute(
                    f"INSERT OR REPLACE INTO trade_records ({columns}) VALUES ({placeholders})",
                    list(data.values())
                )
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"保存到数据库失败: {e}")
    
    def _update_database(self, trade_record: TradeRecord):
        """更新数据库记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE trade_records SET
                        exit_time = ?,
                        exit_price = ?,
                        exit_reason = ?,
                        profit_loss = ?,
                        profit_loss_pct = ?,
                        holding_duration_minutes = ?,
                        market_change_during_trade = ?,
                        max_favorable_excursion = ?,
                        max_adverse_excursion = ?
                    WHERE trade_id = ?
                """, (
                    trade_record.exit_time.isoformat() if trade_record.exit_time else None,
                    trade_record.exit_price,
                    trade_record.exit_reason,
                    trade_record.profit_loss,
                    trade_record.profit_loss_pct,
                    trade_record.holding_duration_minutes,
                    trade_record.market_change_during_trade,
                    trade_record.max_favorable_excursion,
                    trade_record.max_adverse_excursion,
                    trade_record.trade_id
                ))
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"更新数据库失败: {e}")
    
    def _load_from_database(self, trade_id: str) -> Optional[TradeRecord]:
        """从数据库加载交易记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("SELECT * FROM trade_records WHERE trade_id = ?", (trade_id,))
                row = cursor.fetchone()
                
                if row:
                    data = dict(row)
                    # 转换时间字符串为datetime
                    data['entry_time'] = datetime.fromisoformat(data['entry_time'])
                    if data['exit_time']:
                        data['exit_time'] = datetime.fromisoformat(data['exit_time'])
                    
                    # 移除数据库特有字段
                    data.pop('created_at', None)
                    
                    return TradeRecord(**data)
                    
        except Exception as e:
            self.logger.error(f"从数据库加载失败: {e}")
        
        return None
    
    def get_recent_trades(self, days: int = 30, limit: int = 100) -> List[TradeRecord]:
        """获取最近的交易记录"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM trade_records 
                    WHERE entry_time >= ? AND exit_time IS NOT NULL
                    ORDER BY entry_time DESC 
                    LIMIT ?
                """, (cutoff_date.isoformat(), limit))
                
                trades = []
                for row in cursor.fetchall():
                    data = dict(row)
                    data['entry_time'] = datetime.fromisoformat(data['entry_time'])
                    if data['exit_time']:
                        data['exit_time'] = datetime.fromisoformat(data['exit_time'])
                    data.pop('created_at', None)
                    trades.append(TradeRecord(**data))
                
                return trades
                
        except Exception as e:
            self.logger.error(f"获取最近交易记录失败: {e}")
            return []
    
    def get_statistics(self, days: int = 30) -> Dict:
        """获取交易统计"""
        try:
            trades = self.get_recent_trades(days)
            
            if not trades:
                return {'total_trades': 0}
            
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t.profit_loss > 0])
            win_rate = winning_trades / total_trades
            
            total_profit = sum(t.profit_loss for t in trades)
            avg_profit_per_trade = total_profit / total_trades
            
            avg_holding_time = sum(t.holding_duration_minutes for t in trades) / total_trades
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'win_rate': win_rate,
                'total_profit': total_profit,
                'avg_profit_per_trade': avg_profit_per_trade,
                'avg_holding_time_minutes': avg_holding_time
            }
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {'error': str(e)}
