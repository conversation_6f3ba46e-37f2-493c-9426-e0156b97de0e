"""
QuantumForex Pro - 简化技术分析器
不依赖TA-Lib的轻量级技术分析引擎
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

from config.config import config

class SignalStrength(Enum):
    """信号强度枚举"""
    VERY_STRONG = 5
    STRONG = 4
    MODERATE = 3
    WEAK = 2
    VERY_WEAK = 1

@dataclass
class TechnicalSignal:
    """技术信号数据结构"""
    signal_type: str
    direction: str
    strength: SignalStrength
    confidence: float
    value: float
    timestamp: str

class SimpleTechnicalAnalyzer:
    """
    简化技术分析器
    使用纯Python实现的技术指标
    """
    
    def __init__(self):
        self.config = config.TECHNICAL_CONFIG
    
    def analyze_comprehensive(self, data: pd.DataFrame, symbol: str, timeframe: str = '15min') -> Dict:
        """
        综合技术分析
        
        Args:
            data: OHLCV数据
            symbol: 交易品种
            timeframe: 时间框架
            
        Returns:
            Dict: 综合分析结果
        """
        try:
            # 数据预处理
            df = self._prepare_data(data)
            
            if len(df) < 20:
                return self._create_insufficient_data_response(len(df), symbol)
            
            # 1. 趋势分析
            trend_analysis = self._analyze_trend(df)
            
            # 2. 动量分析
            momentum_analysis = self._analyze_momentum(df)
            
            # 3. 波动率分析
            volatility_analysis = self._analyze_volatility(df)
            
            # 4. 成交量分析
            volume_analysis = self._analyze_volume(df)
            
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'timestamp': df.index[-1].isoformat() if hasattr(df.index[-1], 'isoformat') else str(df.index[-1]),
                'data_points': len(df),
                'trend_analysis': trend_analysis,
                'momentum_analysis': momentum_analysis,
                'volatility_analysis': volatility_analysis,
                'volume_analysis': volume_analysis,
                'analysis_status': 'success'
            }
            
        except Exception as e:
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'error': f'技术分析失败: {str(e)}',
                'analysis_status': 'error'
            }
    
    def _prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """数据预处理"""
        df = data.copy()
        
        # 确保列名标准化
        df.columns = [col.lower() for col in df.columns]
        
        # 计算基础价格指标
        df['hl2'] = (df['high'] + df['low']) / 2
        df['hlc3'] = (df['high'] + df['low'] + df['close']) / 3
        df['ohlc4'] = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        
        # 计算收益率
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        return df
    
    def _analyze_trend(self, df: pd.DataFrame) -> Dict:
        """趋势分析"""
        try:
            # 计算移动平均线
            sma_20 = df['close'].rolling(20).mean()
            sma_50 = df['close'].rolling(50).mean()
            ema_12 = df['close'].ewm(span=12).mean()
            ema_26 = df['close'].ewm(span=26).mean()
            
            current_price = df['close'].iloc[-1]
            
            # 趋势判断
            trend_signals = []
            
            # 价格与均线关系
            if current_price > sma_20.iloc[-1]:
                trend_signals.append(1)  # 看涨
            else:
                trend_signals.append(-1)  # 看跌
            
            if current_price > sma_50.iloc[-1]:
                trend_signals.append(1)
            else:
                trend_signals.append(-1)
            
            # EMA交叉
            if ema_12.iloc[-1] > ema_26.iloc[-1]:
                trend_signals.append(1)
            else:
                trend_signals.append(-1)
            
            # 计算趋势评分
            trend_score = sum(trend_signals) / len(trend_signals)
            
            # 确定整体趋势
            if trend_score > 0.5:
                overall_trend = "BULLISH"
            elif trend_score < -0.5:
                overall_trend = "BEARISH"
            else:
                overall_trend = "NEUTRAL"
            
            return {
                'trend_score': trend_score,
                'overall_trend': overall_trend,
                'sma_20': sma_20.iloc[-1],
                'sma_50': sma_50.iloc[-1],
                'ema_12': ema_12.iloc[-1],
                'ema_26': ema_26.iloc[-1],
                'price_vs_sma20': 'ABOVE' if current_price > sma_20.iloc[-1] else 'BELOW',
                'price_vs_sma50': 'ABOVE' if current_price > sma_50.iloc[-1] else 'BELOW'
            }
            
        except Exception as e:
            return {'error': f'趋势分析失败: {str(e)}'}
    
    def _analyze_momentum(self, df: pd.DataFrame) -> Dict:
        """动量分析"""
        try:
            # 计算RSI
            rsi_14 = self._calculate_rsi(df['close'], 14)
            
            # 计算MACD
            macd, macd_signal, macd_hist = self._calculate_macd(df['close'])
            
            # 计算ROC
            roc_10 = ((df['close'] / df['close'].shift(10)) - 1) * 100
            
            # 动量信号
            momentum_signals = []
            
            # RSI信号
            current_rsi = rsi_14.iloc[-1]
            if current_rsi > 70:
                momentum_signals.append(-1)  # 超买
            elif current_rsi < 30:
                momentum_signals.append(1)   # 超卖
            elif current_rsi > 50:
                momentum_signals.append(0.5) # 偏多
            else:
                momentum_signals.append(-0.5) # 偏空
            
            # MACD信号
            if macd.iloc[-1] > macd_signal.iloc[-1]:
                momentum_signals.append(1)
            else:
                momentum_signals.append(-1)
            
            # ROC信号
            if roc_10.iloc[-1] > 0:
                momentum_signals.append(1)
            else:
                momentum_signals.append(-1)
            
            # 计算动量评分
            momentum_score = sum(momentum_signals) / len(momentum_signals)
            
            # 确定整体动量
            if momentum_score > 0.3:
                overall_momentum = "BULLISH"
            elif momentum_score < -0.3:
                overall_momentum = "BEARISH"
            else:
                overall_momentum = "NEUTRAL"
            
            return {
                'momentum_score': momentum_score,
                'overall_momentum': overall_momentum,
                'rsi_14': current_rsi,
                'macd': macd.iloc[-1],
                'macd_signal': macd_signal.iloc[-1],
                'macd_histogram': macd_hist.iloc[-1],
                'roc_10': roc_10.iloc[-1]
            }
            
        except Exception as e:
            return {'error': f'动量分析失败: {str(e)}'}
    
    def _analyze_volatility(self, df: pd.DataFrame) -> Dict:
        """波动率分析"""
        try:
            # 计算布林带
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(df['close'])
            
            # 计算ATR
            atr = self._calculate_atr(df['high'], df['low'], df['close'])
            
            # 计算历史波动率
            volatility = df['returns'].rolling(20).std() * np.sqrt(252)  # 年化波动率
            
            current_price = df['close'].iloc[-1]
            
            # 布林带位置
            bb_position = (current_price - bb_lower.iloc[-1]) / (bb_upper.iloc[-1] - bb_lower.iloc[-1])
            
            # 波动率状态
            current_vol = volatility.iloc[-1]
            avg_vol = volatility.rolling(50).mean().iloc[-1]
            
            if current_vol > avg_vol * 1.5:
                volatility_regime = "HIGH"
            elif current_vol < avg_vol * 0.7:
                volatility_regime = "LOW"
            else:
                volatility_regime = "NORMAL"
            
            return {
                'volatility_score': current_vol / avg_vol if avg_vol > 0 else 1.0,
                'volatility_regime': volatility_regime,
                'bb_upper': bb_upper.iloc[-1],
                'bb_middle': bb_middle.iloc[-1],
                'bb_lower': bb_lower.iloc[-1],
                'bb_position': bb_position,
                'atr': atr.iloc[-1],
                'historical_volatility': current_vol
            }
            
        except Exception as e:
            return {'error': f'波动率分析失败: {str(e)}'}
    
    def _analyze_volume(self, df: pd.DataFrame) -> Dict:
        """成交量分析"""
        try:
            if 'volume' not in df.columns:
                return {
                    'volume_trend': 'NEUTRAL',
                    'volume_strength': 0.5,
                    'error': '无成交量数据'
                }
            
            # 成交量移动平均
            volume_ma = df['volume'].rolling(20).mean()
            current_volume = df['volume'].iloc[-1]
            
            # 成交量趋势
            if current_volume > volume_ma.iloc[-1] * 1.2:
                volume_trend = "INCREASING"
                volume_strength = 0.8
            elif current_volume < volume_ma.iloc[-1] * 0.8:
                volume_trend = "DECREASING"
                volume_strength = 0.3
            else:
                volume_trend = "NEUTRAL"
                volume_strength = 0.5
            
            return {
                'volume_trend': volume_trend,
                'volume_strength': volume_strength,
                'current_volume': current_volume,
                'volume_ma': volume_ma.iloc[-1],
                'volume_ratio': current_volume / volume_ma.iloc[-1] if volume_ma.iloc[-1] > 0 else 1.0
            }
            
        except Exception as e:
            return {'error': f'成交量分析失败: {str(e)}'}
    
    def _calculate_rsi(self, data: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_macd(self, data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9):
        """计算MACD"""
        ema_fast = data.ewm(span=fast).mean()
        ema_slow = data.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        macd_hist = macd - macd_signal
        return macd, macd_signal, macd_hist
    
    def _calculate_bollinger_bands(self, data: pd.Series, period: int = 20, std_dev: float = 2.0):
        """计算布林带"""
        sma = data.rolling(window=period).mean()
        std = data.rolling(window=period).std()
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower
    
    def _calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14):
        """计算ATR"""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window=period).mean()
    
    def _create_insufficient_data_response(self, data_points: int, symbol: str) -> Dict:
        """创建数据不足的响应"""
        return {
            'symbol': symbol,
            'error': f'数据不足，需要至少20个数据点，当前只有{data_points}个',
            'analysis_status': 'insufficient_data',
            'trend_analysis': {'trend_score': 0, 'overall_trend': 'NEUTRAL'},
            'momentum_analysis': {'momentum_score': 0, 'overall_momentum': 'NEUTRAL'},
            'volatility_analysis': {'volatility_score': 1.0, 'volatility_regime': 'NORMAL'},
            'volume_analysis': {'volume_trend': 'NEUTRAL', 'volume_strength': 0.5}
        }
