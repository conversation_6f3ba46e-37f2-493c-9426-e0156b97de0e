"""
QuantumForex Pro - 数据库客户端
提供数据库连接和查询功能
"""

import pymysql
import logging
from typing import List, Dict, Any, Optional
from contextlib import contextmanager

# 配置日志
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '',
    'database': 'forex_trading',
    'charset': 'utf8mb4',
    'autocommit': True,
    'connect_timeout': 10,
    'read_timeout': 30,
    'write_timeout': 30
}

@contextmanager
def get_connection():
    """
    获取数据库连接的上下文管理器

    Returns:
        pymysql.Connection: 数据库连接对象
    """
    connection = None
    try:
        connection = pymysql.connect(**DB_CONFIG)
        logger.debug("数据库连接成功")
        yield connection
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        if connection:
            connection.rollback()
        raise
    finally:
        if connection:
            connection.close()
            logger.debug("数据库连接已关闭")

def execute_query(sql: str, params: tuple = None, fetch_one: bool = False) -> Optional[List[Dict[str, Any]]]:
    """
    执行SQL查询

    Args:
        sql (str): SQL查询语句
        params (tuple, optional): 查询参数
        fetch_one (bool): 是否只返回一条记录

    Returns:
        List[Dict[str, Any]]: 查询结果列表
    """
    try:
        with get_connection() as conn:
            with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, params)

                if fetch_one:
                    result = cursor.fetchone()
                    return [result] if result else []
                else:
                    result = cursor.fetchall()
                    return list(result) if result else []

    except Exception as e:
        logger.error(f"执行SQL查询失败: {e}")
        logger.error(f"SQL: {sql}")
        logger.error(f"参数: {params}")
        return []

def execute_update(sql: str, params: tuple = None) -> int:
    """
    执行SQL更新操作

    Args:
        sql (str): SQL更新语句
        params (tuple, optional): 更新参数

    Returns:
        int: 受影响的行数
    """
    try:
        with get_connection() as conn:
            with conn.cursor() as cursor:
                affected_rows = cursor.execute(sql, params)
                conn.commit()
                logger.debug(f"SQL更新成功，影响行数: {affected_rows}")
                return affected_rows

    except Exception as e:
        logger.error(f"执行SQL更新失败: {e}")
        logger.error(f"SQL: {sql}")
        logger.error(f"参数: {params}")
        return 0

def test_connection() -> bool:
    """
    测试数据库连接

    Returns:
        bool: 连接是否成功
    """
    try:
        with get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                return result is not None
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return False

def get_table_info(table_name: str) -> List[Dict[str, Any]]:
    """
    获取表结构信息

    Args:
        table_name (str): 表名

    Returns:
        List[Dict[str, Any]]: 表结构信息
    """
    sql = f"DESCRIBE {table_name}"
    return execute_query(sql)

def check_table_exists(table_name: str) -> bool:
    """
    检查表是否存在

    Args:
        table_name (str): 表名

    Returns:
        bool: 表是否存在
    """
    sql = """
    SELECT COUNT(*) as count
    FROM information_schema.tables
    WHERE table_schema = %s AND table_name = %s
    """
    result = execute_query(sql, (DB_CONFIG['database'], table_name), fetch_one=True)
    return result[0]['count'] > 0 if result else False
