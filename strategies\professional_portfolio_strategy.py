"""
专业投资组合策略
基于现代投资组合理论和量化交易最佳实践
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

class MarketRegime(Enum):
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"

class PortfolioAction(Enum):
    ENTER_LONG = "enter_long"
    ENTER_SHORT = "enter_short"
    EXIT_POSITION = "exit_position"
    HEDGE_POSITION = "hedge_position"
    REBALANCE = "rebalance"
    HOLD = "hold"

@dataclass
class CurrencyPairAnalysis:
    symbol: str
    trend_strength: float
    volatility: float
    momentum: float
    support_resistance: Dict
    correlation_score: float
    market_regime: MarketRegime

@dataclass
class PortfolioDecision:
    action: PortfolioAction
    symbol: str
    size: float
    entry_price: float
    stop_loss: float
    take_profit: float
    confidence: float
    reasoning: str
    risk_reward_ratio: float

class ProfessionalPortfolioStrategy:
    """
    专业投资组合策略

    核心理念：
    1. 基于货币相关性的组合构建
    2. 动态风险平价
    3. 多时间框架确认
    4. 智能仓位管理
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 货币相关性矩阵（基于历史数据，移除黄金，完善7个外汇货币对）
        self.correlation_matrix = {
            'EURUSD': {'GBPUSD': 0.85, 'AUDUSD': 0.75, 'NZDUSD': 0.70, 'USDCHF': -0.80, 'USDCAD': -0.65, 'USDJPY': -0.45},
            'GBPUSD': {'EURUSD': 0.85, 'AUDUSD': 0.70, 'NZDUSD': 0.65, 'USDCHF': -0.75, 'USDCAD': -0.60, 'USDJPY': -0.40},
            'AUDUSD': {'EURUSD': 0.75, 'GBPUSD': 0.70, 'NZDUSD': 0.85, 'USDCHF': -0.65, 'USDCAD': 0.60, 'USDJPY': -0.35},
            'NZDUSD': {'EURUSD': 0.70, 'GBPUSD': 0.65, 'AUDUSD': 0.85, 'USDCHF': -0.60, 'USDCAD': 0.55, 'USDJPY': -0.30},
            'USDCHF': {'EURUSD': -0.80, 'GBPUSD': -0.75, 'AUDUSD': -0.65, 'NZDUSD': -0.60, 'USDCAD': -0.50, 'USDJPY': 0.60},
            'USDCAD': {'EURUSD': -0.65, 'GBPUSD': -0.60, 'AUDUSD': 0.60, 'NZDUSD': 0.55, 'USDCHF': -0.50, 'USDJPY': -0.25},
            'USDJPY': {'EURUSD': -0.45, 'GBPUSD': -0.40, 'AUDUSD': -0.35, 'NZDUSD': -0.30, 'USDCHF': 0.60, 'USDCAD': -0.25}
        }

        # 策略参数
        self.max_portfolio_risk = 0.02  # 最大组合风险2%
        self.max_correlation_exposure = 0.70  # 最大相关性暴露
        self.rebalance_threshold = 0.05  # 重新平衡阈值
        self.min_confidence_threshold = 0.65  # 最小置信度阈值

        # 当前持仓状态
        self.current_positions = {}
        self.portfolio_metrics = {}

    def analyze_market_regime(self, market_data: Dict) -> Dict[str, MarketRegime]:
        """分析市场状态"""
        regimes = {}

        for symbol, data in market_data.items():
            if 'ohlcv' not in data or data['ohlcv'].empty:
                continue

            df = data['ohlcv']

            # 计算趋势强度
            sma_20 = df['close'].rolling(20).mean()
            sma_50 = df['close'].rolling(50).mean()
            trend_strength = (sma_20.iloc[-1] - sma_50.iloc[-1]) / sma_50.iloc[-1]

            # 计算波动率
            returns = df['close'].pct_change()
            volatility = returns.rolling(20).std().iloc[-1] * np.sqrt(252)

            # 判断市场状态
            if abs(trend_strength) > 0.02:
                if trend_strength > 0:
                    regime = MarketRegime.TRENDING_UP
                else:
                    regime = MarketRegime.TRENDING_DOWN
            elif volatility > 0.20:
                regime = MarketRegime.HIGH_VOLATILITY
            elif volatility < 0.10:
                regime = MarketRegime.LOW_VOLATILITY
            else:
                regime = MarketRegime.RANGING

            regimes[symbol] = regime

        return regimes

    def calculate_portfolio_correlation(self, positions: Dict) -> float:
        """计算投资组合相关性"""
        if len(positions) < 2:
            return 0.0

        total_correlation = 0.0
        pair_count = 0

        symbols = list(positions.keys())
        for i in range(len(symbols)):
            for j in range(i + 1, len(symbols)):
                symbol1, symbol2 = symbols[i], symbols[j]

                # 获取相关性
                correlation = self.correlation_matrix.get(symbol1, {}).get(symbol2, 0.0)

                # 权重调整
                weight1 = positions[symbol1].get('weight', 0.0)
                weight2 = positions[symbol2].get('weight', 0.0)

                total_correlation += correlation * weight1 * weight2
                pair_count += 1

        return total_correlation / max(pair_count, 1)

    def generate_portfolio_decisions(self, market_data: Dict, current_positions: Dict) -> List[PortfolioDecision]:
        """生成投资组合决策"""
        try:
            decisions = []

            # 1. 分析市场状态
            market_regimes = self.analyze_market_regime(market_data)

            # 2. 分析每个货币对
            pair_analyses = {}
            for symbol in market_data.keys():
                analysis = self._analyze_currency_pair(symbol, market_data[symbol], market_regimes.get(symbol))
                if analysis:
                    pair_analyses[symbol] = analysis

            # 3. 检查当前组合风险
            portfolio_risk = self._calculate_portfolio_risk(current_positions, pair_analyses)

            # 4. 生成交易决策
            if portfolio_risk > self.max_portfolio_risk:
                # 风险过高，减仓或对冲
                decisions.extend(self._generate_risk_reduction_decisions(current_positions, pair_analyses))
            else:
                # 寻找新的交易机会
                decisions.extend(self._generate_opportunity_decisions(pair_analyses, current_positions))

            # 5. 检查组合平衡
            rebalance_decisions = self._check_rebalancing_needs(current_positions, pair_analyses)
            decisions.extend(rebalance_decisions)

            # 6. 过滤低置信度决策
            decisions = [d for d in decisions if d.confidence >= self.min_confidence_threshold]

            return decisions

        except Exception as e:
            self.logger.error(f"生成投资组合决策失败: {e}")
            return []

    def _analyze_currency_pair(self, symbol: str, data: Dict, regime: MarketRegime) -> Optional[CurrencyPairAnalysis]:
        """分析单个货币对"""
        try:
            if 'ohlcv' not in data or data['ohlcv'].empty:
                return None

            df = data['ohlcv']

            # 趋势强度分析
            trend_strength = self._calculate_trend_strength(df)

            # 波动率分析
            volatility = self._calculate_volatility(df)

            # 动量分析
            momentum = self._calculate_momentum(df)

            # 支撑阻力分析
            support_resistance = self._find_support_resistance(df)

            # 相关性评分
            correlation_score = self._calculate_correlation_score(symbol)

            return CurrencyPairAnalysis(
                symbol=symbol,
                trend_strength=trend_strength,
                volatility=volatility,
                momentum=momentum,
                support_resistance=support_resistance,
                correlation_score=correlation_score,
                market_regime=regime
            )

        except Exception as e:
            self.logger.error(f"分析货币对{symbol}失败: {e}")
            return None

    def _calculate_trend_strength(self, df: pd.DataFrame) -> float:
        """计算趋势强度"""
        # 使用多个时间框架的移动平均线
        sma_10 = df['close'].rolling(10).mean()
        sma_20 = df['close'].rolling(20).mean()
        sma_50 = df['close'].rolling(50).mean()

        current_price = df['close'].iloc[-1]

        # 计算价格相对于均线的位置
        trend_score = 0.0

        if current_price > sma_10.iloc[-1]:
            trend_score += 0.3
        if current_price > sma_20.iloc[-1]:
            trend_score += 0.4
        if current_price > sma_50.iloc[-1]:
            trend_score += 0.3

        # 计算均线排列
        if sma_10.iloc[-1] > sma_20.iloc[-1] > sma_50.iloc[-1]:
            trend_score += 0.5  # 多头排列
        elif sma_10.iloc[-1] < sma_20.iloc[-1] < sma_50.iloc[-1]:
            trend_score -= 0.5  # 空头排列

        return max(-1.0, min(1.0, trend_score))

    def _calculate_volatility(self, df: pd.DataFrame) -> float:
        """计算波动率"""
        returns = df['close'].pct_change().dropna()
        return returns.rolling(20).std().iloc[-1] * np.sqrt(252)

    def _calculate_momentum(self, df: pd.DataFrame) -> float:
        """计算动量"""
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 标准化到-1到1
        return (rsi.iloc[-1] - 50) / 50

    def _find_support_resistance(self, df: pd.DataFrame) -> Dict:
        """寻找支撑阻力位"""
        try:
            current_price = df['close'].iloc[-1]

            # 计算ATR用于动态支撑阻力
            atr = self._calculate_atr(df)

            # 寻找近期关键价位（5-10天内）
            recent_highs = df['high'].rolling(5, center=True).max().dropna()
            recent_lows = df['low'].rolling(5, center=True).min().dropna()

            # 过滤出当前价格附近的关键位
            price_range = atr * 3  # ATR的3倍作为有效范围

            # 寻找支撑位（当前价格下方）
            valid_supports = recent_lows[
                (recent_lows < current_price) &
                (recent_lows > current_price - price_range)
            ]

            # 寻找阻力位（当前价格上方）
            valid_resistances = recent_highs[
                (recent_highs > current_price) &
                (recent_highs < current_price + price_range)
            ]

            # 选择最近的支撑阻力位
            if len(valid_supports) > 0:
                support = valid_supports.iloc[-1]  # 最近的支撑
            else:
                support = current_price - atr * 2  # 默认支撑

            if len(valid_resistances) > 0:
                resistance = valid_resistances.iloc[-1]  # 最近的阻力
            else:
                resistance = current_price + atr * 2  # 默认阻力

            return {
                'resistance': resistance,
                'support': support,
                'current_price': current_price,
                'atr': atr
            }

        except Exception as e:
            self.logger.error(f"计算支撑阻力位失败: {e}")
            current_price = df['close'].iloc[-1]
            atr = 0.002  # 默认ATR
            return {
                'resistance': current_price + atr,
                'support': current_price - atr,
                'current_price': current_price,
                'atr': atr
            }

    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """计算平均真实波幅"""
        try:
            if len(df) < period + 1:
                return 0.002  # 默认ATR

            high = df['high']
            low = df['low']
            close = df['close']

            # 计算真实波幅
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))

            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = tr.rolling(period).mean().iloc[-1]

            return atr if not pd.isna(atr) else 0.002

        except Exception:
            return 0.002

    def _calculate_correlation_score(self, symbol: str) -> float:
        """计算相关性评分"""
        # 基于当前持仓计算相关性风险
        if not self.current_positions:
            return 0.0

        total_correlation = 0.0
        for pos_symbol in self.current_positions.keys():
            correlation = self.correlation_matrix.get(symbol, {}).get(pos_symbol, 0.0)
            total_correlation += abs(correlation)

        return total_correlation / len(self.current_positions)

    def _calculate_portfolio_risk(self, positions: Dict, analyses: Dict) -> float:
        """计算投资组合风险"""
        if not positions:
            return 0.0

        total_risk = 0.0
        for symbol, position in positions.items():
            if symbol in analyses:
                analysis = analyses[symbol]
                position_risk = position.get('size', 0.0) * analysis.volatility
                total_risk += position_risk ** 2

        return np.sqrt(total_risk)

    def _generate_opportunity_decisions(self, analyses: Dict, current_positions: Dict) -> List[PortfolioDecision]:
        """生成交易机会决策"""
        decisions = []

        for symbol, analysis in analyses.items():
            # 跳过已有持仓的货币对（避免重复开仓）
            if symbol in current_positions:
                continue

            # 检查相关性风险
            if analysis.correlation_score > self.max_correlation_exposure:
                continue

            # 生成交易信号
            decision = self._generate_trade_signal(analysis)
            if decision:
                decisions.append(decision)

        return decisions

    def _generate_trade_signal(self, analysis: CurrencyPairAnalysis) -> Optional[PortfolioDecision]:
        """生成交易信号"""
        # 综合评分
        signal_strength = (analysis.trend_strength + analysis.momentum) / 2

        # 置信度计算
        confidence = abs(signal_strength) * (1 - analysis.correlation_score)

        if confidence < self.min_confidence_threshold:
            return None

        # 确定交易方向
        if signal_strength > 0.3:
            action = PortfolioAction.ENTER_LONG
        elif signal_strength < -0.3:
            action = PortfolioAction.ENTER_SHORT
        else:
            return None

        # 计算入场价格和止损止盈
        current_price = analysis.support_resistance['current_price']
        entry_price = current_price
        atr = analysis.support_resistance.get('atr', 0.002)

        # 基于ATR的实用止损止盈计算
        stop_loss, take_profit = self._calculate_practical_stop_take_profit(
            entry_price, action, atr, analysis.symbol
        )

        # 计算仓位大小（基于波动率调整）
        base_size = 0.01
        volatility_adjustment = max(0.5, min(2.0, 0.15 / analysis.volatility))
        position_size = base_size * volatility_adjustment

        return PortfolioDecision(
            action=action,
            symbol=analysis.symbol,
            size=position_size,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            confidence=confidence,
            reasoning=f"趋势强度: {analysis.trend_strength:.2f}, 动量: {analysis.momentum:.2f}, 市场状态: {analysis.market_regime.value}",
            risk_reward_ratio=abs(take_profit - entry_price) / abs(entry_price - stop_loss)
        )

    def _generate_risk_reduction_decisions(self, positions: Dict, analyses: Dict) -> List[PortfolioDecision]:
        """生成风险降低决策"""
        decisions = []

        # 找出风险最高的持仓
        risk_scores = {}
        for symbol, position in positions.items():
            if symbol in analyses:
                analysis = analyses[symbol]
                risk_score = position.get('size', 0.0) * analysis.volatility * analysis.correlation_score
                risk_scores[symbol] = risk_score

        # 按风险排序
        sorted_risks = sorted(risk_scores.items(), key=lambda x: x[1], reverse=True)

        # 减少最高风险的持仓
        for symbol, risk_score in sorted_risks[:2]:  # 最多处理2个最高风险持仓
            decision = PortfolioDecision(
                action=PortfolioAction.EXIT_POSITION,
                symbol=symbol,
                size=positions[symbol].get('size', 0.0) * 0.5,  # 减仓50%
                entry_price=0.0,
                stop_loss=0.0,
                take_profit=0.0,
                confidence=0.8,
                reasoning=f"风险管理：降低高风险持仓，风险评分: {risk_score:.3f}",
                risk_reward_ratio=0.0
            )
            decisions.append(decision)

        return decisions

    def _check_rebalancing_needs(self, positions: Dict, analyses: Dict) -> List[PortfolioDecision]:
        """检查重新平衡需求"""
        decisions = []

        if not positions:
            return decisions

        # 计算当前权重
        total_value = sum(pos.get('value', 0.0) for pos in positions.values())
        if total_value == 0:
            return decisions

        # 检查是否需要重新平衡
        for symbol, position in positions.items():
            current_weight = position.get('value', 0.0) / total_value
            target_weight = 1.0 / len(positions)  # 等权重

            weight_diff = abs(current_weight - target_weight)
            if weight_diff > self.rebalance_threshold:
                # 需要重新平衡
                if current_weight > target_weight:
                    # 减仓
                    reduce_size = position.get('size', 0.0) * (weight_diff / current_weight)
                    decision = PortfolioDecision(
                        action=PortfolioAction.REBALANCE,
                        symbol=symbol,
                        size=-reduce_size,  # 负数表示减仓
                        entry_price=0.0,
                        stop_loss=0.0,
                        take_profit=0.0,
                        confidence=0.7,
                        reasoning=f"重新平衡：当前权重{current_weight:.2%}，目标权重{target_weight:.2%}",
                        risk_reward_ratio=0.0
                    )
                    decisions.append(decision)

        return decisions

    def _calculate_practical_stop_take_profit(self, entry_price: float, action: PortfolioAction,
                                            atr: float, symbol: str) -> tuple:
        """计算实用的止损止盈

        基于以下原则：
        1. 止损距离基于ATR，通常1.5-2.5倍ATR
        2. 止盈距离为止损的1.5-2倍
        3. 考虑不同货币对的特性
        4. 确保合理的点数范围
        """
        try:
            # 货币对特定配置（移除黄金，保留7个外汇货币对）
            currency_configs = {
                'EURUSD': {'min_stop_pips': 15, 'max_stop_pips': 30, 'pip_value': 0.0001},
                'GBPUSD': {'min_stop_pips': 18, 'max_stop_pips': 35, 'pip_value': 0.0001},
                'AUDUSD': {'min_stop_pips': 16, 'max_stop_pips': 32, 'pip_value': 0.0001},
                'NZDUSD': {'min_stop_pips': 18, 'max_stop_pips': 35, 'pip_value': 0.0001},
                'USDCHF': {'min_stop_pips': 15, 'max_stop_pips': 30, 'pip_value': 0.0001},
                'USDCAD': {'min_stop_pips': 16, 'max_stop_pips': 32, 'pip_value': 0.0001},
                'USDJPY': {'min_stop_pips': 15, 'max_stop_pips': 30, 'pip_value': 0.01}
            }

            config = currency_configs.get(symbol, {
                'min_stop_pips': 15, 'max_stop_pips': 30, 'pip_value': 0.0001
            })

            pip_value = config['pip_value']
            min_stop_pips = config['min_stop_pips']
            max_stop_pips = config['max_stop_pips']

            # 基于ATR计算止损距离
            atr_multiplier = 2.0  # 2倍ATR作为止损
            stop_distance = atr * atr_multiplier

            # 转换为点数并限制范围
            stop_pips = stop_distance / pip_value
            stop_pips = max(min_stop_pips, min(max_stop_pips, stop_pips))

            # 转换回价格距离
            final_stop_distance = stop_pips * pip_value

            # 计算止盈距离（1.8倍止损距离）
            risk_reward_ratio = 1.8
            take_profit_distance = final_stop_distance * risk_reward_ratio

            if action == PortfolioAction.ENTER_LONG:
                stop_loss = entry_price - final_stop_distance
                take_profit = entry_price + take_profit_distance
            else:  # ENTER_SHORT
                stop_loss = entry_price + final_stop_distance
                take_profit = entry_price - take_profit_distance

            # 确保价格精度
            if symbol == 'USDJPY':
                stop_loss = round(stop_loss, 3)
                take_profit = round(take_profit, 3)
            else:
                stop_loss = round(stop_loss, 5)
                take_profit = round(take_profit, 5)

            return stop_loss, take_profit

        except Exception as e:
            self.logger.error(f"计算止损止盈失败: {e}")
            # 返回保守的默认值（20点止损，36点止盈）
            default_stop_distance = 20 * 0.0001  # 20点
            default_tp_distance = 36 * 0.0001    # 36点

            if action == PortfolioAction.ENTER_LONG:
                return entry_price - default_stop_distance, entry_price + default_tp_distance
            else:
                return entry_price + default_stop_distance, entry_price - default_tp_distance