"""
检查QuantumForex_Pro数据库状态
"""

import sqlite3
import os

def check_database():
    db_path = "data/trade_results.db"
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    try:
        with sqlite3.connect(db_path) as conn:
            # 检查总记录数
            cursor = conn.execute("SELECT COUNT(*) FROM trade_records")
            total_count = cursor.fetchone()[0]
            print(f"📊 总交易记录数: {total_count}")
            
            # 检查已平仓记录数
            cursor = conn.execute("SELECT COUNT(*) FROM trade_records WHERE exit_time IS NOT NULL")
            closed_count = cursor.fetchone()[0]
            print(f"📊 已平仓记录数: {closed_count}")
            
            # 检查未平仓记录数
            cursor = conn.execute("SELECT COUNT(*) FROM trade_records WHERE exit_time IS NULL")
            open_count = cursor.fetchone()[0]
            print(f"📊 未平仓记录数: {open_count}")
            
            # 显示前5条记录
            print(f"\n📋 前5条记录:")
            cursor = conn.execute("SELECT trade_id, symbol, action, entry_time, exit_time FROM trade_records LIMIT 5")
            for i, row in enumerate(cursor.fetchall(), 1):
                trade_id, symbol, action, entry_time, exit_time = row
                status = "已平仓" if exit_time else "未平仓"
                print(f"   {i}. {trade_id[:20]}... - {symbol} {action} - {status}")
            
            # 检查最近30天的已平仓记录
            from datetime import datetime, timedelta
            cutoff_date = datetime.now() - timedelta(days=30)
            cursor = conn.execute("""
                SELECT COUNT(*) FROM trade_records 
                WHERE entry_time >= ? AND exit_time IS NOT NULL
            """, (cutoff_date.isoformat(),))
            recent_closed = cursor.fetchone()[0]
            print(f"\n📊 最近30天已平仓记录: {recent_closed}")
            
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")

if __name__ == "__main__":
    check_database()
