#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试MT4集成系统
验证MT4订单跟踪、交易结果同步功能
"""

import sys
import os
import time
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mt4_order_tracker():
    """测试MT4订单跟踪器"""
    try:
        print("🔍 测试MT4订单跟踪器...")
        
        from core.mt4_integration import MT4OrderTracker
        
        tracker = MT4OrderTracker()
        
        # 注册回调函数
        def on_order_opened(order):
            print(f"📈 订单开仓回调: {order.ticket} {order.symbol} {order.order_type}")
        
        def on_order_closed(order):
            print(f"📉 订单关闭回调: {order.ticket} 盈亏: ${order.profit:.2f}")
        
        tracker.register_order_opened_callback(on_order_opened)
        tracker.register_order_closed_callback(on_order_closed)
        
        # 启动监控
        tracker.start_monitoring()
        print("✅ MT4订单跟踪器启动成功")
        
        # 运行一段时间
        print("⏳ 监控MT4订单变化（30秒）...")
        time.sleep(30)
        
        # 获取最近订单
        recent_orders = tracker.get_recent_orders(hours=24)
        print(f"📊 最近24小时订单数量: {len(recent_orders)}")
        
        for order in recent_orders[:5]:  # 显示最近5个订单
            print(f"   📋 {order.ticket}: {order.symbol} {order.order_type} "
                  f"${order.profit:.2f} ({order.status})")
        
        # 停止监控
        tracker.stop_monitoring()
        print("✅ MT4订单跟踪器测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ MT4订单跟踪器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trade_result_sync():
    """测试交易结果同步"""
    try:
        print("\n🔄 测试交易结果同步...")
        
        from core.learning_system import LearningCoordinator
        from core.mt4_integration import TradeResultSync
        
        # 创建学习协调器
        learning_coordinator = LearningCoordinator()
        learning_coordinator.start()
        
        # 创建同步器
        sync = TradeResultSync(learning_coordinator)
        sync.start()
        
        print("✅ 交易结果同步启动成功")
        
        # 模拟注册系统交易
        trade_data = {
            'symbol': 'EURUSD',
            'action': 'BUY',
            'entry_price': 1.0850,
            'volume': 0.01,
            'stop_loss': 1.0830,
            'take_profit': 1.0890
        }
        
        success = sync.register_system_trade("test_trade_001", trade_data)
        if success:
            print("✅ 系统交易注册成功")
        
        # 运行一段时间等待匹配
        print("⏳ 等待交易匹配（30秒）...")
        time.sleep(30)
        
        # 获取同步统计
        stats = sync.get_sync_statistics()
        print(f"📊 同步统计: {stats}")
        
        # 强制同步最近交易
        print("🔄 强制同步最近交易...")
        sync.force_sync_recent_trades(hours=1)
        
        # 停止同步
        sync.stop()
        learning_coordinator.stop()
        
        print("✅ 交易结果同步测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 交易结果同步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mt4_trade_monitor():
    """测试MT4交易监控器"""
    try:
        print("\n🎛️ 测试MT4交易监控器...")
        
        from core.learning_system import LearningCoordinator
        from core.mt4_integration import MT4TradeMonitor
        
        # 创建学习协调器
        learning_coordinator = LearningCoordinator()
        learning_coordinator.start()
        
        # 创建监控器
        monitor = MT4TradeMonitor(learning_coordinator)
        monitor.start_monitoring()
        
        print("✅ MT4交易监控器启动成功")
        
        # 运行一段时间
        print("⏳ 监控系统运行（60秒）...")
        
        for i in range(6):  # 每10秒检查一次状态
            time.sleep(10)
            status = monitor.get_monitoring_status()
            print(f"📊 监控状态 ({i+1}/6): "
                  f"运行: {status['monitoring']} | "
                  f"健康评分: {status['system_health']:.1%}")
        
        # 获取最近MT4订单
        recent_orders = monitor.get_recent_mt4_orders(hours=1)
        print(f"📋 最近1小时MT4订单: {len(recent_orders)}个")
        
        # 强制同步所有交易
        print("🔄 强制同步所有交易...")
        monitor.force_sync_all_trades()
        
        # 停止监控
        monitor.stop_monitoring()
        learning_coordinator.stop()
        
        print("✅ MT4交易监控器测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ MT4交易监控器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_with_learning_system():
    """测试与学习系统的集成"""
    try:
        print("\n🧠 测试与学习系统的集成...")
        
        from core.learning_system import LearningCoordinator
        from core.mt4_integration import MT4TradeMonitor
        
        # 创建完整的集成系统
        learning_coordinator = LearningCoordinator()
        learning_coordinator.start()
        
        mt4_monitor = MT4TradeMonitor(learning_coordinator)
        mt4_monitor.start_monitoring()
        
        print("✅ 完整集成系统启动成功")
        
        # 模拟系统交易记录
        trade_data = {
            'symbol': 'EURUSD',
            'action': 'BUY',
            'entry_price': 1.0850,
            'volume': 0.01,
            'stop_loss': 1.0830,
            'take_profit': 1.0890,
            'confidence': 0.75,
            'strategy_used': 'portfolio_strategy',
            'market_condition': 'trending',
            'rsi': 65.0,
            'ma_20': 1.0845,
            'ma_50': 1.0840,
            'atr': 0.002,
            'volatility': 0.008
        }
        
        # 记录交易（这会自动注册到MT4同步）
        trade_id = learning_coordinator.record_trade_entry(trade_data)
        print(f"✅ 交易记录并注册到MT4同步: {trade_id}")
        
        # 运行一段时间
        print("⏳ 系统集成运行（45秒）...")
        time.sleep(45)
        
        # 获取学习统计
        learning_stats = learning_coordinator.get_learning_statistics()
        print(f"📊 学习系统统计: {learning_stats['trade_stats']}")
        
        # 获取MT4监控状态
        mt4_status = mt4_monitor.get_monitoring_status()
        print(f"🔗 MT4监控状态: {mt4_status['sync_stats']}")
        
        # 停止系统
        mt4_monitor.stop_monitoring()
        learning_coordinator.stop()
        
        print("✅ 学习系统集成测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 学习系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始MT4集成系统测试")
    print("="*60)
    
    success_count = 0
    total_tests = 4
    
    # 测试MT4订单跟踪器
    if test_mt4_order_tracker():
        success_count += 1
    
    # 测试交易结果同步
    if test_trade_result_sync():
        success_count += 1
    
    # 测试MT4交易监控器
    if test_mt4_trade_monitor():
        success_count += 1
    
    # 测试与学习系统的集成
    if test_integration_with_learning_system():
        success_count += 1
    
    print("\n" + "="*60)
    print(f"🎯 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有MT4集成测试通过！")
        print("✅ MT4订单跟踪功能正常")
        print("✅ 交易结果同步功能正常")
        print("✅ MT4交易监控功能正常")
        print("✅ 学习系统集成功能正常")
        print("\n🏆 MT4真实交易结果反馈系统完全可用！")
        print("🔗 系统现在可以实时获取MT4真实交易结果")
        print("📊 学习系统将基于真实盈亏进行优化")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
        print("💡 提示：确保MT4客户端正在运行且API可访问")
    
    return success_count == total_tests

if __name__ == "__main__":
    main()
