#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试交易信号生成
创建明显的市场趋势，看系统是否能生成交易决策
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_strong_trend_signals():
    """测试强趋势信号生成"""
    try:
        print("🚀 测试强趋势信号生成...")
        
        from strategies.master_strategy_engine import MasterStrategyEngine
        from strategies.professional_portfolio_strategy import PortfolioAction
        import pandas as pd
        import numpy as np
        from datetime import datetime
        
        # 创建主策略引擎
        master_strategy = MasterStrategyEngine()
        
        # 创建强烈上升趋势的EURUSD数据
        dates = pd.date_range(end=datetime.now(), periods=100, freq='1min')
        
        # 强烈上升趋势 - 3%涨幅
        strong_uptrend = np.linspace(0, 0.03, 100)  # 3%上升趋势
        eurusd_prices = 1.0800 + strong_uptrend + np.random.normal(0, 0.0002, 100)  # 减少噪音
        
        # 确保价格单调上升
        for i in range(1, len(eurusd_prices)):
            if eurusd_prices[i] < eurusd_prices[i-1]:
                eurusd_prices[i] = eurusd_prices[i-1] + 0.0001
        
        # 强烈下降趋势的GBPUSD数据
        strong_downtrend = np.linspace(0, -0.025, 100)  # 2.5%下降趋势
        gbpusd_prices = 1.2700 + strong_downtrend + np.random.normal(0, 0.0002, 100)
        
        # 确保价格单调下降
        for i in range(1, len(gbpusd_prices)):
            if gbpusd_prices[i] > gbpusd_prices[i-1]:
                gbpusd_prices[i] = gbpusd_prices[i-1] - 0.0001
        
        # 创建高波动率市场数据
        market_data = {
            'EURUSD': {
                'ohlcv': pd.DataFrame({
                    'open': eurusd_prices + np.random.normal(0, 0.00005, 100),
                    'high': eurusd_prices + np.abs(np.random.normal(0, 0.0005, 100)),
                    'low': eurusd_prices - np.abs(np.random.normal(0, 0.0005, 100)),
                    'close': eurusd_prices,
                    'volume': np.random.randint(5000, 15000, 100)  # 高成交量
                }, index=dates),
                'current_price': eurusd_prices[-1],
                'volatility': 0.025  # 高波动率
            },
            'GBPUSD': {
                'ohlcv': pd.DataFrame({
                    'open': gbpusd_prices + np.random.normal(0, 0.00005, 100),
                    'high': gbpusd_prices + np.abs(np.random.normal(0, 0.0005, 100)),
                    'low': gbpusd_prices - np.abs(np.random.normal(0, 0.0005, 100)),
                    'close': gbpusd_prices,
                    'volume': np.random.randint(5000, 15000, 100)  # 高成交量
                }, index=dates),
                'current_price': gbpusd_prices[-1],
                'volatility': 0.028  # 高波动率
            },
            'AUDUSD': {
                'ohlcv': pd.DataFrame({
                    'open': np.random.normal(0.6420, 0.0005, 100),
                    'high': np.random.normal(0.6425, 0.0005, 100),
                    'low': np.random.normal(0.6415, 0.0005, 100),
                    'close': np.random.normal(0.6420, 0.0005, 100),
                    'volume': np.random.randint(2000, 8000, 100)
                }, index=dates),
                'current_price': 0.6420,
                'volatility': 0.012
            }
        }
        
        print(f"📊 强趋势市场数据:")
        print(f"   EURUSD: {eurusd_prices[0]:.5f} → {eurusd_prices[-1]:.5f} (涨幅: +{(eurusd_prices[-1]/eurusd_prices[0]-1)*100:.2f}%)")
        print(f"   GBPUSD: {gbpusd_prices[0]:.5f} → {gbpusd_prices[-1]:.5f} (跌幅: {(gbpusd_prices[-1]/gbpusd_prices[0]-1)*100:.2f}%)")
        
        # 测试无持仓情况下的强趋势信号
        print("\n📋 测试场景：强趋势 + 无持仓")
        current_positions = {}
        
        master_decision = master_strategy.generate_master_decision(market_data, current_positions)
        
        print(f"   策略类型: {master_decision.strategy_used.value}")
        print(f"   市场条件: {master_decision.market_condition.value}")
        print(f"   置信度: {master_decision.confidence:.2%}")
        print(f"   决策数量: {len(master_decision.decisions)}")
        print(f"   决策说明: {master_decision.reasoning}")
        
        if master_decision.decisions:
            print("   🎯 具体交易决策:")
            for i, decision in enumerate(master_decision.decisions, 1):
                action_desc = {
                    PortfolioAction.ENTER_LONG: "开多仓",
                    PortfolioAction.ENTER_SHORT: "开空仓", 
                    PortfolioAction.EXIT_POSITION: "平仓",
                    PortfolioAction.HEDGE_POSITION: "对冲",
                    PortfolioAction.REBALANCE: "重新平衡",
                    PortfolioAction.HOLD: "持有"
                }.get(decision.action, decision.action.value)
                
                print(f"     {i}. {decision.symbol} - {action_desc}")
                print(f"        仓位: {decision.size:.3f}手")
                print(f"        入场价: {decision.entry_price:.5f}")
                print(f"        止损: {decision.stop_loss:.5f}")
                print(f"        止盈: {decision.take_profit:.5f}")
                print(f"        置信度: {decision.confidence:.2%}")
                print(f"        风险回报比: 1:{decision.risk_reward_ratio:.2f}")
                print(f"        原因: {decision.reasoning}")
                print()
        else:
            print("   ⚠️ 未生成交易决策")
            
        # 分析为什么没有生成信号
        print("\n🔍 分析信号生成过程:")
        
        # 手动分析趋势强度
        for symbol, data in market_data.items():
            df = data['ohlcv']
            if len(df) >= 50:
                sma_20 = df['close'].rolling(20).mean()
                sma_50 = df['close'].rolling(50).mean()
                
                if not sma_20.isna().all() and not sma_50.isna().all():
                    trend_strength = (sma_20.iloc[-1] - sma_50.iloc[-1]) / sma_50.iloc[-1]
                    current_price = df['close'].iloc[-1]
                    
                    print(f"   {symbol}:")
                    print(f"     当前价格: {current_price:.5f}")
                    print(f"     20日均线: {sma_20.iloc[-1]:.5f}")
                    print(f"     50日均线: {sma_50.iloc[-1]:.5f}")
                    print(f"     趋势强度: {trend_strength:.4f}")
                    print(f"     波动率: {data['volatility']:.3f}")
                    
                    # 判断趋势方向
                    if trend_strength > 0.02:
                        print(f"     ✅ 强烈上升趋势")
                    elif trend_strength < -0.02:
                        print(f"     ✅ 强烈下降趋势")
                    else:
                        print(f"     ⚠️ 趋势不明显")
        
        return len(master_decision.decisions) > 0
        
    except Exception as e:
        print(f"❌ 强趋势信号测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_portfolio_strategy_parameters():
    """测试投资组合策略参数"""
    try:
        print("\n🔧 测试投资组合策略参数...")
        
        from strategies.professional_portfolio_strategy import ProfessionalPortfolioStrategy
        
        strategy = ProfessionalPortfolioStrategy()
        
        print(f"   最大组合风险: {strategy.max_portfolio_risk:.1%}")
        print(f"   最大相关性暴露: {strategy.max_correlation_exposure:.1%}")
        print(f"   重新平衡阈值: {strategy.rebalance_threshold:.1%}")
        print(f"   最小置信度阈值: {strategy.min_confidence_threshold:.1%}")
        
        print("\n📊 货币相关性矩阵:")
        for base_currency, correlations in strategy.correlation_matrix.items():
            print(f"   {base_currency}:")
            for target_currency, correlation in correlations.items():
                print(f"     与{target_currency}: {correlation:+.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略参数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始交易信号生成测试")
    print("="*60)
    
    # 测试策略参数
    if not test_portfolio_strategy_parameters():
        return False
    
    # 测试强趋势信号
    signal_generated = test_strong_trend_signals()
    
    if signal_generated:
        print("\n🎉 成功生成交易信号！")
    else:
        print("\n⚠️ 未生成交易信号，这可能是正常的保守行为")
        print("💡 系统优先保护资金安全，只在高质量信号时交易")
    
    print("\n✅ 交易信号测试完成！")
    return True

if __name__ == "__main__":
    main()
