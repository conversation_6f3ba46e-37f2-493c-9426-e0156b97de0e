"""
QuantumForex Pro 配置文件
继承现有项目的成功配置，确保开箱即用
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class QuantumConfig:
    """QuantumForex Pro 核心配置"""

    # ==================== 继承的成功配置 ====================

    # 数据库配置 (继承现有pizza_quotes配置)
    DATABASE_CONFIG = {
        'host': os.getenv('PIZZA_QUOTES_DB_HOST', 'pizza-wnet-db1.mysql.rds.aliyuncs.com'),
        'port': int(os.getenv('PIZZA_QUOTES_DB_PORT', '6688')),
        'user': os.getenv('PIZZA_QUOTES_DB_USER', 'ea_quote_srv'),
        'password': os.getenv('PIZZA_QUOTES_DB_PASSWORD', 'pizza666!'),
        'database': os.getenv('PIZZA_QUOTES_DB_NAME', 'pizza_quotes'),
        'charset': 'utf8mb4'
    }

    # LLM API配置 (继承现有DeepSeek配置)
    LLM_CONFIG = {
        'api_url': os.getenv('LLM_API_URL', 'https://api.siliconflow.cn/v1/chat/completions'),
        'api_key': os.getenv('DEEPSEEK_API_KEY', 'sk-dplvjslhezcjinavtmaporlyumqqwnowcbjwyvmetxychflk'),
        'primary_model': 'Pro/deepseek-ai/DeepSeek-R1',
        'backup_model': 'Pro/deepseek-ai/DeepSeek-V3',
        'max_retries': 3,
        'timeout': (15, 60)
    }

    # MT4连接配置 (继承现有配置)
    MT4_CONFIG = {
        'server_host': os.getenv('MT4_SERVER_HOST', '127.0.0.1'),
        'server_port': int(os.getenv('MT4_SERVER_PORT', '5555')),
        'server_address': f"tcp://{os.getenv('MT4_SERVER_HOST', '127.0.0.1')}:{os.getenv('MT4_SERVER_PORT', '5555')}",
        'auth_code': os.getenv('AUTH_CODE', 'test-auth-code-1'),
        'connection_timeout': 30,
        'max_retries': 5,
        'retry_delay': 3
    }

    # ==================== QuantumForex Pro 新增配置 ====================

    # 系统性能配置 (适配Windows Server 2012)
    SYSTEM_CONFIG = {
        'max_memory_usage': 0.70,      # 最大内存使用率70%
        'max_cpu_usage': 0.60,         # 最大CPU使用率60%
        'max_concurrent_threads': 4,    # 最大并发线程数
        'data_chunk_size': 1000,       # 数据分块大小
        'cache_size_mb': 256,          # 缓存大小256MB
        'gc_frequency': 300            # 垃圾回收频率(秒)
    }

    # 交易配置
    TRADING_CONFIG = {
        # 支持的货币对 (移除黄金交易，保留7个货币对)
        'supported_pairs': [
            'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD',
            'USDCHF', 'USDCAD', 'USDJPY'
        ],

        # 交易时间框架
        'timeframes': {
            'signal_generation': '1min',    # 信号生成频率
            'analysis_update': '5min',      # 分析更新频率
            'llm_optimization': '4hour',    # LLM优化频率
            'risk_check': '1min'           # 风险检查频率
        },

        # 仓位管理
        'position_management': {
            'max_positions': 7,            # 最大持仓数量（移除黄金后调整为7）
            'max_pair_positions': 2,       # 单货币对最大持仓
            'position_size_method': 'fixed_risk',  # 仓位计算方法
            'default_risk_per_trade': 0.02  # 单笔默认风险2%
        }
    }

    # 风险管理配置
    RISK_CONFIG = {
        # 账户级风险
        'account_risk': {
            'max_drawdown': 0.12,          # 最大回撤12%
            'daily_loss_limit': 0.03,      # 日亏损限制3%
            'weekly_loss_limit': 0.08,     # 周亏损限制8%
            'monthly_loss_limit': 0.15     # 月亏损限制15%
        },

        # 策略级风险
        'strategy_risk': {
            'max_single_position': 0.02,   # 单笔最大风险2%
            'max_strategy_exposure': 0.10, # 单策略最大暴露10%
            'correlation_limit': 0.7,      # 相关性限制0.7
            'max_consecutive_losses': 4    # 最大连续亏损4次
        },

        # 市场风险
        'market_risk': {
            'volatility_multiplier': 2.5,  # 波动率倍数限制
            'news_event_buffer': 30,       # 新闻事件缓冲30分钟
            'weekend_position_limit': 0.5, # 周末持仓限制50%
            'liquidity_requirement': 'high' # 流动性要求
        }
    }

    # 技术分析配置
    TECHNICAL_CONFIG = {
        # 趋势分析参数
        'trend_analysis': {
            'ma_periods': [5, 10, 20, 50, 100, 200],
            'ema_periods': [8, 13, 21, 34, 55, 89],
            'adx_period': 14,
            'trend_strength_threshold': 25
        },

        # 动量分析参数
        'momentum_analysis': {
            'rsi_periods': [14, 21],
            'macd_params': [(12, 26, 9), (5, 35, 5)],
            'stoch_params': [(14, 3, 3), (21, 5, 5)],
            'overbought_threshold': 70,
            'oversold_threshold': 30
        },

        # 波动率分析参数
        'volatility_analysis': {
            'bb_periods': [20, 50],
            'bb_std_dev': 2.0,
            'atr_periods': [14, 21],
            'keltner_periods': [20, 10]
        },

        # 成交量分析参数
        'volume_analysis': {
            'volume_periods': [10, 20, 50],
            'vwap_periods': [20, 50],
            'volume_spike_threshold': 2.0
        }
    }

    # 机器学习配置
    ML_CONFIG = {
        # 模型参数 (轻量级，适配老服务器)
        'models': {
            'random_forest': {
                'n_estimators': 50,
                'max_depth': 10,
                'random_state': 42,
                'n_jobs': 2
            },
            'svm': {
                'kernel': 'rbf',
                'C': 1.0,
                'gamma': 'scale'
            },
            'gradient_boosting': {
                'n_estimators': 30,
                'max_depth': 6,
                'learning_rate': 0.1
            }
        },

        # 特征工程
        'feature_engineering': {
            'lookback_periods': [5, 10, 20, 50],
            'feature_selection_method': 'mutual_info',
            'max_features': 50,
            'feature_scaling': 'standard'
        },

        # 模型训练 - 优化为适应真实数据量
        'training': {
            'train_test_split': 0.85,           # 增加训练集比例
            'validation_split': 0.15,          # 减少验证集比例
            'retrain_frequency': 'daily',      # 更频繁的重训练
            'min_training_samples': 50,        # 大幅降低最小样本要求
            'optimal_training_samples': 200,   # 理想样本数
            'retrain_interval_hours': 6        # 6小时重训练间隔
        }
    }

    # LLM策略大脑配置
    LLM_BRAIN_CONFIG = {
        # Token预算管理
        'token_budget': {
            'macro_analyzer': 5000,
            'parameter_optimizer': 8000,
            'risk_assessor': 3000,
            'decision_coordinator': 4000,
            'daily_limit': 50000
        },

        # 分析频率
        'analysis_frequency': {
            'macro_analysis': '1day',
            'parameter_optimization': '1week',
            'risk_assessment': '1day',
            'decision_coordination': '1hour'
        },

        # 提示词模板
        'prompt_templates': {
            'macro_analysis': 'templates/macro_analysis_template.txt',
            'parameter_optimization': 'templates/parameter_optimization_template.txt',
            'risk_assessment': 'templates/risk_assessment_template.txt',
            'decision_coordination': 'templates/decision_coordination_template.txt'
        }
    }

    # 性能目标配置
    PERFORMANCE_TARGETS = {
        # 收益指标
        'return_targets': {
            'annual_return': {'target': 0.30, 'min': 0.25, 'max': 0.40},
            'monthly_return': {'target': 0.025, 'volatility': 0.05},
            'sharpe_ratio': {'target': 2.0, 'min': 1.5}
        },

        # 风险指标
        'risk_targets': {
            'max_drawdown': {'limit': 0.12, 'warning': 0.08},
            'calmar_ratio': {'target': 2.5, 'min': 2.0},
            'var_95': {'limit': 0.03}
        },

        # 交易指标
        'trading_targets': {
            'win_rate': {'target': 0.65, 'min': 0.60},
            'profit_factor': {'target': 1.8, 'min': 1.5},
            'avg_trade_duration': {'target': 24, 'max': 72}
        },

        # 系统指标
        'system_targets': {
            'signal_latency': {'max': 30},
            'execution_slippage': {'max': 1},
            'system_uptime': {'min': 0.99},
            'memory_usage': {'max': 0.70}
        }
    }

    # 监控配置
    MONITORING_CONFIG = {
        'metrics_collection_interval': 60,  # 指标收集间隔(秒)
        'alert_thresholds': {
            'high_memory_usage': 0.80,
            'high_cpu_usage': 0.70,
            'high_drawdown': 0.08,
            'low_win_rate': 0.55
        },
        'dashboard_update_interval': 30,     # 仪表板更新间隔(秒)
        'log_retention_days': 30            # 日志保留天数
    }

# 创建全局配置实例
config = QuantumConfig()
