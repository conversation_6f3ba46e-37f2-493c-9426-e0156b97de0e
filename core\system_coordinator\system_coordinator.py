"""
系统协调器 - 统一管理所有子系统的交互和冲突解决
"""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from enum import Enum
from dataclasses import dataclass
import threading
import queue

class SystemPriority(Enum):
    EMERGENCY = 1      # 紧急情况（风控）
    HIGH = 2          # 高优先级（持仓管理）
    MEDIUM = 3        # 中优先级（正常交易）
    LOW = 4           # 低优先级（LLM分析）
    BACKGROUND = 5    # 后台任务（参数调优）

class SystemState(Enum):
    NORMAL = "normal"
    RISK_CONTROL = "risk_control"
    POSITION_MANAGEMENT = "position_management"
    EMERGENCY_STOP = "emergency_stop"
    MAINTENANCE = "maintenance"

@dataclass
class SystemTask:
    task_id: str
    system_name: str
    priority: SystemPriority
    action: str
    data: Dict
    timestamp: datetime
    timeout: int = 30  # 超时时间（秒）

class SystemCoordinator:
    def __init__(self):
        self.current_state = SystemState.NORMAL
        self.task_queue = queue.PriorityQueue()
        self.active_tasks = {}
        self.system_locks = {}
        self.coordination_rules = self._init_coordination_rules()
        self.last_actions = {}
        self.conflict_resolution_log = []
        
        # 系统状态跟踪
        self.system_status = {
            'trading_system': {'active': True, 'last_action': None},
            'risk_management': {'active': True, 'last_action': None},
            'position_manager': {'active': True, 'last_action': None},
            'llm_analyzer': {'active': True, 'last_action': None},
            'parameter_optimizer': {'active': True, 'last_action': None}
        }
        
        # 启动协调器线程
        self.coordinator_thread = threading.Thread(target=self._coordination_loop, daemon=True)
        self.coordinator_thread.start()
    
    def _init_coordination_rules(self) -> Dict:
        """初始化系统协调规则"""
        return {
            # 冲突解决规则
            'conflict_resolution': {
                # 风控系统优先级最高
                ('risk_management', 'trading_system'): 'risk_management_wins',
                ('risk_management', 'position_manager'): 'coordinate_action',
                ('risk_management', 'llm_analyzer'): 'risk_management_wins',
                
                # 持仓管理与交易系统协调
                ('position_manager', 'trading_system'): 'coordinate_timing',
                ('position_manager', 'llm_analyzer'): 'position_manager_priority',
                
                # LLM分析与其他系统
                ('llm_analyzer', 'trading_system'): 'sequential_execution',
                ('llm_analyzer', 'parameter_optimizer'): 'avoid_conflict',
                
                # 参数调优与其他系统
                ('parameter_optimizer', 'trading_system'): 'pause_trading',
                ('parameter_optimizer', 'position_manager'): 'pause_optimization',
            },
            
            # 时间协调规则
            'timing_rules': {
                'min_interval_between_trades': 60,  # 交易间隔最少60秒
                'position_management_window': 30,   # 持仓管理窗口30秒
                'llm_analysis_cooldown': 300,      # LLM分析冷却期5分钟
                'parameter_optimization_interval': 3600,  # 参数调优间隔1小时
            },
            
            # 资源协调规则
            'resource_rules': {
                'max_concurrent_llm_calls': 1,     # 最多1个并发LLM调用
                'max_concurrent_trades': 2,        # 最多2个并发交易
                'mt4_connection_sharing': True,    # 共享MT4连接
            }
        }
    
    def submit_task(self, system_name: str, action: str, data: Dict, 
                   priority: SystemPriority = SystemPriority.MEDIUM) -> str:
        """提交系统任务"""
        task_id = f"{system_name}_{action}_{int(time.time())}"
        task = SystemTask(
            task_id=task_id,
            system_name=system_name,
            priority=priority,
            action=action,
            data=data,
            timestamp=datetime.now()
        )
        
        # 检查是否有冲突
        conflict_resolution = self._check_conflicts(task)
        
        if conflict_resolution['allow']:
            self.task_queue.put((priority.value, task))
            print(f"📋 任务提交: {system_name}.{action} (优先级: {priority.name})")
            return task_id
        else:
            print(f"⚠️ 任务被拒绝: {system_name}.{action} - {conflict_resolution['reason']}")
            return None
    
    def _check_conflicts(self, new_task: SystemTask) -> Dict:
        """检查任务冲突"""
        try:
            # 检查系统状态
            if self.current_state == SystemState.EMERGENCY_STOP:
                if new_task.priority != SystemPriority.EMERGENCY:
                    return {'allow': False, 'reason': '系统紧急停止状态'}
            
            # 检查时间间隔冲突
            timing_conflict = self._check_timing_conflicts(new_task)
            if not timing_conflict['allow']:
                return timing_conflict
            
            # 检查资源冲突
            resource_conflict = self._check_resource_conflicts(new_task)
            if not resource_conflict['allow']:
                return resource_conflict
            
            # 检查逻辑冲突
            logic_conflict = self._check_logic_conflicts(new_task)
            if not logic_conflict['allow']:
                return logic_conflict
            
            return {'allow': True, 'reason': '无冲突'}
            
        except Exception as e:
            print(f"❌ 冲突检查失败: {e}")
            return {'allow': False, 'reason': f'冲突检查异常: {e}'}
    
    def _check_timing_conflicts(self, task: SystemTask) -> Dict:
        """检查时间冲突"""
        system_name = task.system_name
        action = task.action
        
        # 检查最小间隔
        if system_name in self.last_actions:
            last_time = self.last_actions[system_name].get('timestamp')
            if last_time:
                time_diff = (datetime.now() - last_time).total_seconds()
                
                # 交易系统间隔检查
                if system_name == 'trading_system' and action == 'execute_trade':
                    min_interval = self.coordination_rules['timing_rules']['min_interval_between_trades']
                    if time_diff < min_interval:
                        return {'allow': False, 'reason': f'交易间隔不足，需等待{min_interval-time_diff:.0f}秒'}
                
                # LLM分析冷却期检查
                if system_name == 'llm_analyzer' and action == 'analyze':
                    cooldown = self.coordination_rules['timing_rules']['llm_analysis_cooldown']
                    if time_diff < cooldown:
                        return {'allow': False, 'reason': f'LLM分析冷却期，需等待{cooldown-time_diff:.0f}秒'}
        
        return {'allow': True, 'reason': '时间检查通过'}
    
    def _check_resource_conflicts(self, task: SystemTask) -> Dict:
        """检查资源冲突"""
        # 检查LLM并发限制
        if task.system_name == 'llm_analyzer':
            active_llm_tasks = sum(1 for t in self.active_tasks.values() 
                                 if t.system_name == 'llm_analyzer')
            max_concurrent = self.coordination_rules['resource_rules']['max_concurrent_llm_calls']
            if active_llm_tasks >= max_concurrent:
                return {'allow': False, 'reason': 'LLM并发限制'}
        
        # 检查交易并发限制
        if task.system_name == 'trading_system' and task.action == 'execute_trade':
            active_trades = sum(1 for t in self.active_tasks.values() 
                              if t.system_name == 'trading_system' and t.action == 'execute_trade')
            max_concurrent = self.coordination_rules['resource_rules']['max_concurrent_trades']
            if active_trades >= max_concurrent:
                return {'allow': False, 'reason': '交易并发限制'}
        
        return {'allow': True, 'reason': '资源检查通过'}
    
    def _check_logic_conflicts(self, task: SystemTask) -> Dict:
        """检查逻辑冲突"""
        # 检查是否有相互冲突的活跃任务
        for active_task in self.active_tasks.values():
            conflict_key = (active_task.system_name, task.system_name)
            reverse_key = (task.system_name, active_task.system_name)
            
            if conflict_key in self.coordination_rules['conflict_resolution']:
                resolution = self.coordination_rules['conflict_resolution'][conflict_key]
                return self._apply_conflict_resolution(task, active_task, resolution)
            elif reverse_key in self.coordination_rules['conflict_resolution']:
                resolution = self.coordination_rules['conflict_resolution'][reverse_key]
                return self._apply_conflict_resolution(active_task, task, resolution)
        
        return {'allow': True, 'reason': '逻辑检查通过'}
    
    def _apply_conflict_resolution(self, task1: SystemTask, task2: SystemTask, resolution: str) -> Dict:
        """应用冲突解决策略"""
        if resolution == 'risk_management_wins':
            if task1.system_name == 'risk_management':
                return {'allow': True, 'reason': '风控优先'}
            else:
                return {'allow': False, 'reason': '风控系统优先，拒绝执行'}
        
        elif resolution == 'coordinate_timing':
            # 协调执行时间，延迟低优先级任务
            if task1.priority.value < task2.priority.value:
                return {'allow': True, 'reason': '高优先级任务'}
            else:
                return {'allow': False, 'reason': '等待高优先级任务完成'}
        
        elif resolution == 'sequential_execution':
            return {'allow': False, 'reason': '等待前序任务完成'}
        
        elif resolution == 'pause_trading':
            if task2.system_name == 'trading_system':
                return {'allow': False, 'reason': '参数调优期间暂停交易'}
            return {'allow': True, 'reason': '允许参数调优'}
        
        return {'allow': True, 'reason': '默认允许'}
    
    def _coordination_loop(self):
        """协调器主循环"""
        while True:
            try:
                # 处理任务队列
                if not self.task_queue.empty():
                    priority, task = self.task_queue.get(timeout=1)
                    self._execute_coordinated_task(task)
                
                # 清理超时任务
                self._cleanup_timeout_tasks()
                
                # 更新系统状态
                self._update_system_state()
                
                time.sleep(0.1)  # 避免CPU占用过高
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ 协调器循环异常: {e}")
                time.sleep(1)
    
    def _execute_coordinated_task(self, task: SystemTask):
        """执行协调后的任务"""
        try:
            print(f"⚡ 执行协调任务: {task.system_name}.{task.action}")
            
            # 记录任务开始
            self.active_tasks[task.task_id] = task
            self.last_actions[task.system_name] = {
                'action': task.action,
                'timestamp': datetime.now(),
                'task_id': task.task_id
            }
            
            # 这里应该调用实际的系统方法
            # 由于是协调器，我们只记录和管理，实际执行由各子系统完成
            
            # 模拟任务执行时间
            time.sleep(0.1)
            
            # 任务完成，从活跃任务中移除
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            
            print(f"✅ 任务完成: {task.system_name}.{task.action}")
            
        except Exception as e:
            print(f"❌ 任务执行失败: {task.system_name}.{task.action} - {e}")
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
    
    def _cleanup_timeout_tasks(self):
        """清理超时任务"""
        current_time = datetime.now()
        timeout_tasks = []
        
        for task_id, task in self.active_tasks.items():
            if (current_time - task.timestamp).total_seconds() > task.timeout:
                timeout_tasks.append(task_id)
        
        for task_id in timeout_tasks:
            task = self.active_tasks[task_id]
            print(f"⏰ 任务超时: {task.system_name}.{task.action}")
            del self.active_tasks[task_id]
    
    def _update_system_state(self):
        """更新系统状态"""
        # 根据活跃任务和系统状况更新状态
        if any(task.system_name == 'risk_management' and task.priority == SystemPriority.EMERGENCY 
               for task in self.active_tasks.values()):
            self.current_state = SystemState.EMERGENCY_STOP
        elif any(task.system_name == 'risk_management' for task in self.active_tasks.values()):
            self.current_state = SystemState.RISK_CONTROL
        elif any(task.system_name == 'position_manager' for task in self.active_tasks.values()):
            self.current_state = SystemState.POSITION_MANAGEMENT
        else:
            self.current_state = SystemState.NORMAL
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        return {
            'current_state': self.current_state.value,
            'active_tasks': len(self.active_tasks),
            'queue_size': self.task_queue.qsize(),
            'system_status': self.system_status,
            'last_actions': self.last_actions
        }
    
    def emergency_stop(self, reason: str):
        """紧急停止所有系统"""
        print(f"🚨 系统紧急停止: {reason}")
        self.current_state = SystemState.EMERGENCY_STOP
        
        # 清空任务队列
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
            except queue.Empty:
                break
        
        # 记录紧急停止
        self.conflict_resolution_log.append({
            'timestamp': datetime.now(),
            'action': 'emergency_stop',
            'reason': reason
        })
    
    def resume_normal_operation(self):
        """恢复正常运行"""
        print("✅ 系统恢复正常运行")
        self.current_state = SystemState.NORMAL
