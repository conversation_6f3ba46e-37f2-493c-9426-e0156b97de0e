"""
MT4客户端工具
用于与MT4服务器通信，执行交易操作
支持多用户和授权验证（授权验证由MT4 Server-V2处理）
"""
import os
import json
import uuid
import time
import zmq
from dotenv import load_dotenv
from datetime import datetime

# 加载环境变量
load_dotenv()

# 检查是否跳过MT4连接
def should_skip_mt4_connection():
    """
    智能判断是否应该跳过MT4连接

    Returns:
        bool: True表示应该跳过MT4连接，False表示正常连接
    """
    # 首先检查手动设置的环境变量
    manual_skip = os.environ.get('SKIP_MT4_CONNECTION', '').lower()
    if manual_skip == 'true':
        return True
    elif manual_skip == 'false':
        return False

    # 如果没有手动设置，则根据市场时间自动判断
    try:
        # 简化的市场时间检查，避免循环导入
        from datetime import datetime
        now = datetime.now()
        weekday = now.weekday()

        # 周末不交易
        if weekday >= 5:  # 周六、周日
            return True

        # 周五晚上到周一早上不交易
        if weekday == 4 and now.hour >= 22:  # 周五22点后
            return True

        if weekday == 0 and now.hour < 6:  # 周一6点前
            return True

        return False
    except Exception as e:
        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] ⚠️ 无法检查市场时间，默认不跳过MT4连接: {e}')
        return False

# 动态获取跳过状态
def get_skip_mt4_status():
    """获取当前MT4跳过状态"""
    return should_skip_mt4_connection()

# MT4服务器地址
MT4_SERVER_ADDRESS = os.getenv('MT4_SERVER_ADDRESS', 'tcp://127.0.0.1:5555')
print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] MT4服务器地址: {MT4_SERVER_ADDRESS}')
# 授权码（可选）
AUTH_CODE = os.getenv('AUTH_CODE', None)


class MT4Client:
    """MT4客户端类，支持多用户和授权验证"""

    def __init__(self, server_address=None, auth_code=None):
        """
        初始化MT4客户端

        Args:
            server_address (str, optional): MT4服务器地址
            auth_code (str, optional): 授权码
        """
        self.server_address = server_address or MT4_SERVER_ADDRESS
        self.auth_code = auth_code or AUTH_CODE  # 使用传入的授权码或环境变量中的授权码
        self.user_info = None
        self.socket = None
        self.context = None
        self.is_connected = False
        self.is_authorized = False  # 授权状态由MT4 Server-V2验证
        self.request_timeout = 60000  # 请求超时时间（毫秒），增加到60秒，提高稳定性
        self.max_retries = 5  # 最大重试次数，增加到5次
        self.retry_delay = 3  # 重试延迟（秒），增加到3秒
        self.last_connect_attempt = 0  # 上次连接尝试时间
        self.connect_cooldown = 3  # 连接冷却时间（秒），减少到3秒，提高响应速度
        self.connecting = False  # 连接锁，防止多线程同时连接
        self.connect_lock_time = 0  # 连接锁设置时间
        self.connect_lock_timeout = 60  # 连接锁超时时间（秒），增加到60秒
        self.connection_id = 0  # 连接ID，用于跟踪连接操作
        self.heartbeat_interval = 60  # 心跳检测间隔（秒）
        self.last_heartbeat = time.time()  # 上次心跳时间
        self.heartbeat_enabled = True  # 是否启用心跳检测

    def set_auth_code(self, auth_code):
        """
        设置授权码

        Args:
            auth_code (str): 授权码
        """
        self.auth_code = auth_code
        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 设置授权码: {auth_code}')
        # 授权验证将在连接MT4服务器时进行

    def connect(self):
        """
        连接到MT4服务器，包含重试机制和连接状态管理
        如果设置了授权码，会在连接时发送授权信息

        Returns:
            bool: 连接是否成功
        """
        # 检查是否跳过MT4连接
        if should_skip_mt4_connection():
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] ⚠️  MT4服务器跳过模式已启用，模拟连接成功')
            self.is_connected = True
            self.is_authorized = True
            self.user_info = {
                'id': 'test-user',
                'username': '测试用户',
                'expiry_date': '2025-12-31',
                'account_type': '测试账户'
            }
            return True

        # 如果设置了授权码，记录日志
        if self.auth_code:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 使用授权码连接MT4服务器: {self.auth_code}')
        # 检查是否已经连接
        if self.is_connected and self.socket:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 已经连接到MT4服务器，无需重新连接')
            return True

        # 检查连接锁是否超时
        current_time = time.time()
        if self.connecting:
            # 如果连接锁已经超时，强制释放
            if current_time - self.connect_lock_time > self.connect_lock_timeout:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接锁已超时 ({self.connect_lock_timeout}秒)，强制释放')
                self.connecting = False
            else:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 已有连接操作正在进行中，跳过本次连接请求')
                return False

        # 检查连接冷却时间
        if current_time - self.last_connect_attempt < self.connect_cooldown:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接请求过于频繁，请等待 {self.connect_cooldown - (current_time - self.last_connect_attempt):.1f} 秒后再试')
            return False

        # 设置连接锁和连接ID
        self.connecting = True
        self.connect_lock_time = current_time
        self.last_connect_attempt = current_time
        self.connection_id += 1
        connection_id = self.connection_id

        now = datetime.now()
        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接操作 #{connection_id} 开始')

        try:
            # 无论如何，先断开现有连接并清理资源
            self.disconnect()

            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 正在连接到MT4服务器: {self.server_address}')

            # 创建新的ZMQ上下文和套接字
            if self.context is None:
                self.context = zmq.Context()

            # 确保创建新的套接字
            self.socket = self.context.socket(zmq.REQ)

            # 设置超时时间
            self.socket.setsockopt(zmq.RCVTIMEO, self.request_timeout)

            # 连接到服务器
            self.socket.connect(self.server_address)

            # 使用重试机制发送ping请求测试连接
            for retry in range(self.max_retries):
                try:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 发送ping请求测试连接... (尝试 {retry+1}/{self.max_retries})')

                    # 直接使用socket发送ping请求，避免递归调用send_request
                    request_id = str(uuid.uuid4())
                    request = {'action': 'ping', 'requestId': request_id}

                    # 如果设置了授权码，添加到请求中
                    if self.auth_code:
                        request['auth_code'] = self.auth_code
                        request['auth_type'] = 'client'  # 标识为客户端授权

                    request_str = json.dumps(request)

                    self.socket.send_string(request_str)
                    response_str = self.socket.recv_string()

                    # 解析响应
                    response = json.loads(response_str)

                    if response and response.get('status') == 'success':
                        self.is_connected = True
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 成功连接到MT4服务器')

                        # 检查授权状态
                        if 'auth_status' in response:
                            self.is_authorized = response.get('auth_status') == 'valid'
                            if self.is_authorized:
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 授权验证成功，用户: {response.get("username", "未知")}')
                                # 保存用户信息
                                self.user_info = {
                                    'id': response.get('user_id', ''),
                                    'username': response.get('username', '未知用户'),
                                    'expiry_date': response.get('expiry_date', ''),
                                    'account_type': response.get('account_type', '标准账户')
                                }
                            else:
                                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 授权验证失败: {response.get("auth_message", "未知错误")}')

                        self.connecting = False
                        return True
                    else:
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] MT4服务器连接测试失败: {response}')

                        # 如果不是最后一次重试，等待后再试
                        if retry < self.max_retries - 1:
                            wait_time = self.retry_delay * (retry + 1)  # 指数退避
                            now = datetime.now()
                            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                            time.sleep(wait_time)
                        else:
                            self.is_connected = False

                except zmq.error.Again:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] MT4服务器响应超时 (尝试 {retry+1}/{self.max_retries})')

                    # 如果不是最后一次重试，等待后再试
                    if retry < self.max_retries - 1:
                        wait_time = self.retry_delay * (retry + 1)  # 指数退避
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                        time.sleep(wait_time)
                    else:
                        self.is_connected = False

                except Exception as ping_error:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 发送ping请求失败: {ping_error} (尝试 {retry+1}/{self.max_retries})')

                    # 如果不是最后一次重试，等待后再试
                    if retry < self.max_retries - 1:
                        wait_time = self.retry_delay * (retry + 1)  # 指数退避
                        now = datetime.now()
                        print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 等待 {wait_time} 秒后重试...')
                        time.sleep(wait_time)
                    else:
                        self.is_connected = False

            # 所有重试都失败
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接操作 #{connection_id} 失败: 所有重试都失败')
            self.is_connected = False
            self.connecting = False
            return False

        except Exception as error:
            now = datetime.now()
            print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接操作 #{connection_id} 失败: {error}')
            self.is_connected = False

            # 释放连接锁
            self.connecting = False
            return False
        finally:
            # 确保在任何情况下都释放连接锁
            if not self.is_connected:
                self.connecting = False
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 连接操作 #{connection_id} 结束，连接锁已释放')

    def disconnect(self):
        """断开与MT4服务器的连接"""
        if self.socket:
            try:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 正在断开与MT4服务器的连接')

                try:
                    # 尝试断开连接
                    self.socket.disconnect(self.server_address)
                except Exception as disconnect_error:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 断开连接时出错: {disconnect_error}')

                try:
                    # 尝试关闭套接字
                    self.socket.close()
                except Exception as close_error:
                    now = datetime.now()
                    print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 关闭套接字时出错: {close_error}')

                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 已断开与MT4服务器的连接')
            except Exception as error:
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 断开MT4服务器连接时出错: {error}')
            finally:
                # 无论如何，都确保清理资源
                self.socket = None
                self.is_connected = False
                now = datetime.now()
                print(f'[{now.strftime("%Y-%m-%d %H:%M:%S")}] 已清理连接资源')

    def send_request(self, request):
        """
        发送请求到MT4服务器

        Args:
            request (dict): 请求数据

        Returns:
            dict: 响应数据
        """
        # 检查是否跳过MT4连接
        if should_skip_mt4_connection():
            # 模拟响应
            action = request.get('action', 'unknown')
            if action == 'ping':
                return {'status': 'success', 'message': '模拟ping成功'}
            elif action == 'get_market_info':
                symbol = request.get('symbol', 'EURUSD')
                return {
                    'status': 'success',
                    'data': {
                        'symbol': symbol,
                        'bid': 1.0850,
                        'ask': 1.0852,
                        'spread': 2
                    }
                }
            elif action == 'get_positions':
                return {'status': 'success', 'data': []}
            else:
                return {'status': 'success', 'message': f'模拟{action}成功'}

        if not self.is_connected:
            if not self.connect():
                return {'status': 'error', 'message': 'MT4服务器连接失败'}

        try:
            request_str = json.dumps(request)
            self.socket.send_string(request_str)
            response_str = self.socket.recv_string()
            return json.loads(response_str)
        except Exception as e:
            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] 发送请求失败: {e}')
            return {'status': 'error', 'message': str(e)}

    def get_market_info(self, symbol):
        """
        获取市场信息

        Args:
            symbol (str): 货币对符号

        Returns:
            dict: 市场信息
        """
        request = {
            'action': 'get_market_info',
            'symbol': symbol,
            'requestId': str(uuid.uuid4())
        }
        return self.send_request(request)

    def get_positions(self):
        """
        获取当前持仓

        Returns:
            dict: 持仓信息
        """
        request = {
            'action': 'get_positions',
            'requestId': str(uuid.uuid4())
        }
        return self.send_request(request)

    def place_order(self, symbol, order_type, volume, price=None, stop_loss=None, take_profit=None):
        """
        下单

        Args:
            symbol (str): 货币对符号
            order_type (str): 订单类型 (BUY, SELL)
            volume (float): 交易量
            price (float, optional): 价格
            stop_loss (float, optional): 止损价格
            take_profit (float, optional): 止盈价格

        Returns:
            dict: 下单结果
        """
        request = {
            'action': 'place_order',
            'symbol': symbol,
            'type': order_type,
            'volume': volume,
            'requestId': str(uuid.uuid4())
        }

        if price is not None:
            request['price'] = price
        if stop_loss is not None:
            request['stop_loss'] = stop_loss
        if take_profit is not None:
            request['take_profit'] = take_profit

        return self.send_request(request)

    def close_position(self, order_id):
        """
        平仓

        Args:
            order_id (str): 订单ID

        Returns:
            dict: 平仓结果
        """
        request = {
            'action': 'close_position',
            'order_id': order_id,
            'requestId': str(uuid.uuid4())
        }
        return self.send_request(request)

    def get_active_orders(self):
        """
        获取活跃订单

        Returns:
            dict: 活跃订单信息
        """
        request = {
            'action': 'get_active_orders',
            'requestId': str(uuid.uuid4())
        }
        return self.send_request(request)

    def buy(self, symbol, volume, price=None, stop_loss=None, take_profit=None):
        """
        买入

        Args:
            symbol (str): 货币对符号
            volume (float): 交易量
            price (float, optional): 价格
            stop_loss (float, optional): 止损价格
            take_profit (float, optional): 止盈价格

        Returns:
            dict: 买入结果
        """
        return self.place_order(symbol, 'BUY', volume, price, stop_loss, take_profit)

    def sell(self, symbol, volume, price=None, stop_loss=None, take_profit=None):
        """
        卖出

        Args:
            symbol (str): 货币对符号
            volume (float): 交易量
            price (float, optional): 价格
            stop_loss (float, optional): 止损价格
            take_profit (float, optional): 止盈价格

        Returns:
            dict: 卖出结果
        """
        return self.place_order(symbol, 'SELL', volume, price, stop_loss, take_profit)


# 创建全局MT4客户端实例
mt4_client = MT4Client()
