"""
QuantumForex Pro - 高级技术分析器
世界顶级的技术分析引擎，集成50+高级技术指标
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

# 简化的技术指标计算函数（替代TA-Lib）
def simple_sma(data, period):
    """简单移动平均"""
    return data.rolling(window=period).mean()

def simple_ema(data, period):
    """指数移动平均"""
    return data.ewm(span=period).mean()

def simple_rsi(data, period=14):
    """相对强弱指数"""
    delta = data.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def simple_macd(data, fast=12, slow=26, signal=9):
    """MACD指标"""
    ema_fast = simple_ema(data, fast)
    ema_slow = simple_ema(data, slow)
    macd = ema_fast - ema_slow
    macd_signal = simple_ema(macd, signal)
    macd_hist = macd - macd_signal
    return macd, macd_signal, macd_hist

def simple_bollinger_bands(data, period=20, std_dev=2):
    """布林带"""
    sma = simple_sma(data, period)
    std = data.rolling(window=period).std()
    upper = sma + (std * std_dev)
    lower = sma - (std * std_dev)
    return upper, sma, lower

def simple_atr(high, low, close, period=14):
    """平均真实波幅"""
    tr1 = high - low
    tr2 = abs(high - close.shift(1))
    tr3 = abs(low - close.shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    return tr.rolling(window=period).mean()

from config.config import config

class SignalStrength(Enum):
    """信号强度枚举"""
    VERY_STRONG = 5
    STRONG = 4
    MODERATE = 3
    WEAK = 2
    VERY_WEAK = 1

class TrendDirection(Enum):
    """趋势方向枚举"""
    STRONG_BULLISH = 2
    BULLISH = 1
    NEUTRAL = 0
    BEARISH = -1
    STRONG_BEARISH = -2

@dataclass
class TechnicalSignal:
    """技术信号数据结构"""
    signal_type: str
    direction: str
    strength: SignalStrength
    confidence: float
    value: float
    threshold: float
    timeframe: str
    timestamp: str

class AdvancedTechnicalAnalyzer:
    """
    高级技术分析器
    集成世界顶级的技术分析方法
    """

    def __init__(self):
        self.config = config.TECHNICAL_CONFIG
        self.signals_cache = {}
        self.last_analysis_time = None

    def analyze_comprehensive(self, data: pd.DataFrame, symbol: str, timeframe: str = '15min') -> Dict:
        """
        综合技术分析 - 主入口函数

        Args:
            data: OHLCV数据
            symbol: 交易品种
            timeframe: 时间框架

        Returns:
            Dict: 综合分析结果
        """
        try:
            # 数据预处理
            df = self._prepare_data(data)

            if len(df) < 50:  # 确保有足够的数据
                return self._create_insufficient_data_response(len(df), symbol)

            # 1. 趋势分析 (40%权重)
            trend_analysis = self._analyze_trend_comprehensive(df)

            # 2. 动量分析 (30%权重)
            momentum_analysis = self._analyze_momentum_comprehensive(df)

            # 3. 波动率分析 (20%权重)
            volatility_analysis = self._analyze_volatility_comprehensive(df)

            # 4. 成交量分析 (10%权重)
            volume_analysis = self._analyze_volume_comprehensive(df)

            # 5. 高级模式识别
            pattern_analysis = self._analyze_patterns_comprehensive(df)

            # 6. 市场微观结构分析
            microstructure_analysis = self._analyze_microstructure(df)

            # 7. 综合信号生成
            composite_signal = self._generate_composite_signal(
                trend_analysis, momentum_analysis, volatility_analysis,
                volume_analysis, pattern_analysis, microstructure_analysis
            )

            # 8. 信号质量评估
            signal_quality = self._assess_signal_quality(composite_signal, df)

            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'timestamp': df.index[-1].isoformat() if hasattr(df.index[-1], 'isoformat') else str(df.index[-1]),
                'data_points': len(df),
                'trend_analysis': trend_analysis,
                'momentum_analysis': momentum_analysis,
                'volatility_analysis': volatility_analysis,
                'volume_analysis': volume_analysis,
                'pattern_analysis': pattern_analysis,
                'microstructure_analysis': microstructure_analysis,
                'composite_signal': composite_signal,
                'signal_quality': signal_quality,
                'analysis_status': 'success'
            }

        except Exception as e:
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'error': f'技术分析失败: {str(e)}',
                'analysis_status': 'error'
            }

    def _prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """数据预处理和特征工程"""
        df = data.copy()

        # 确保列名标准化
        df.columns = [col.lower() for col in df.columns]

        # 计算基础价格指标
        df['hl2'] = (df['high'] + df['low']) / 2
        df['hlc3'] = (df['high'] + df['low'] + df['close']) / 3
        df['ohlc4'] = (df['open'] + df['high'] + df['low'] + df['close']) / 4

        # 计算收益率
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))

        # 计算真实波幅
        df['true_range'] = np.maximum(
            df['high'] - df['low'],
            np.maximum(
                abs(df['high'] - df['close'].shift(1)),
                abs(df['low'] - df['close'].shift(1))
            )
        )

        return df

    def _analyze_trend_comprehensive(self, df: pd.DataFrame) -> Dict:
        """综合趋势分析"""
        try:
            trend_signals = []

            # 1. 多重移动平均线分析
            ma_analysis = self._analyze_moving_averages(df)
            trend_signals.extend(ma_analysis['signals'])

            # 2. 自适应移动平均线
            adaptive_ma = self._calculate_adaptive_ma(df)

            # 3. ADX趋势强度分析
            adx_analysis = self._analyze_adx_system(df)

            # 4. Aroon指标分析
            aroon_analysis = self._analyze_aroon(df)

            # 5. 线性回归趋势
            regression_analysis = self._analyze_linear_regression(df)

            # 6. 趋势质量评估
            trend_quality = self._assess_trend_quality(df, ma_analysis, adx_analysis)

            # 综合趋势评分
            trend_score = self._calculate_trend_score(
                ma_analysis, adx_analysis, aroon_analysis, regression_analysis
            )

            return {
                'ma_analysis': ma_analysis,
                'adaptive_ma': adaptive_ma,
                'adx_analysis': adx_analysis,
                'aroon_analysis': aroon_analysis,
                'regression_analysis': regression_analysis,
                'trend_quality': trend_quality,
                'trend_score': trend_score,
                'overall_trend': self._determine_overall_trend(trend_score),
                'signals': trend_signals
            }

        except Exception as e:
            return {'error': f'趋势分析失败: {str(e)}'}

    def _analyze_moving_averages(self, df: pd.DataFrame) -> Dict:
        """移动平均线分析"""
        ma_signals = []
        ma_data = {}

        # 计算多重移动平均线
        for period in self.config['trend_analysis']['ma_periods']:
            if len(df) >= period:
                sma = simple_sma(df['close'], period)
                ema = simple_ema(df['close'], period)

                ma_data[f'sma_{period}'] = sma.iloc[-1]
                ma_data[f'ema_{period}'] = ema.iloc[-1]

                # 价格与均线关系
                price_vs_sma = 1 if df['close'].iloc[-1] > sma.iloc[-1] else -1
                price_vs_ema = 1 if df['close'].iloc[-1] > ema.iloc[-1] else -1

                # 均线斜率
                if len(sma) >= 5:
                    sma_slope = (sma.iloc[-1] - sma.iloc[-5]) / 5
                    ema_slope = (ema.iloc[-1] - ema.iloc[-5]) / 5

                    ma_signals.append({
                        'type': f'MA_{period}',
                        'price_vs_sma': price_vs_sma,
                        'price_vs_ema': price_vs_ema,
                        'sma_slope': sma_slope,
                        'ema_slope': ema_slope,
                        'trend_strength': abs(sma_slope) * 10000
                    })

        # EMA交叉信号
        ema_crossovers = self._detect_ema_crossovers(df)

        return {
            'ma_data': ma_data,
            'signals': ma_signals,
            'ema_crossovers': ema_crossovers
        }

    def _calculate_adaptive_ma(self, df: pd.DataFrame) -> Dict:
        """计算自适应移动平均线"""
        try:
            # Kaufman's Adaptive Moving Average (KAMA)
            kama = talib.KAMA(df['close'], timeperiod=30)

            # MESA Adaptive Moving Average (简化版)
            # 使用Hilbert Transform - Instantaneous Trendline
            ht_trendline = talib.HT_TRENDLINE(df['close'])

            return {
                'kama': kama.iloc[-1] if not pd.isna(kama.iloc[-1]) else None,
                'ht_trendline': ht_trendline.iloc[-1] if not pd.isna(ht_trendline.iloc[-1]) else None,
                'price_vs_kama': 1 if df['close'].iloc[-1] > kama.iloc[-1] else -1,
                'price_vs_ht': 1 if df['close'].iloc[-1] > ht_trendline.iloc[-1] else -1
            }

        except Exception:
            return {'error': '自适应移动平均线计算失败'}

    def _analyze_adx_system(self, df: pd.DataFrame) -> Dict:
        """ADX系统分析"""
        try:
            period = self.config['trend_analysis']['adx_period']

            adx = talib.ADX(df['high'], df['low'], df['close'], timeperiod=period)
            plus_di = talib.PLUS_DI(df['high'], df['low'], df['close'], timeperiod=period)
            minus_di = talib.MINUS_DI(df['high'], df['low'], df['close'], timeperiod=period)

            current_adx = adx.iloc[-1]
            current_plus_di = plus_di.iloc[-1]
            current_minus_di = minus_di.iloc[-1]

            # 趋势强度判断
            threshold = self.config['trend_analysis']['trend_strength_threshold']
            if current_adx > threshold:
                trend_strength = "STRONG"
            elif current_adx > threshold * 0.7:
                trend_strength = "MODERATE"
            else:
                trend_strength = "WEAK"

            # 趋势方向判断
            if current_plus_di > current_minus_di:
                trend_direction = "BULLISH"
            else:
                trend_direction = "BEARISH"

            return {
                'adx': current_adx,
                'plus_di': current_plus_di,
                'minus_di': current_minus_di,
                'trend_strength': trend_strength,
                'trend_direction': trend_direction,
                'di_spread': abs(current_plus_di - current_minus_di)
            }

        except Exception:
            return {'error': 'ADX分析失败'}

    def _analyze_aroon(self, df: pd.DataFrame) -> Dict:
        """Aroon指标分析"""
        try:
            aroon_down, aroon_up = talib.AROON(df['high'], df['low'], timeperiod=25)
            aroon_osc = talib.AROONOSC(df['high'], df['low'], timeperiod=25)

            return {
                'aroon_up': aroon_up.iloc[-1],
                'aroon_down': aroon_down.iloc[-1],
                'aroon_oscillator': aroon_osc.iloc[-1],
                'trend_signal': 'BULLISH' if aroon_osc.iloc[-1] > 0 else 'BEARISH'
            }

        except Exception:
            return {'error': 'Aroon分析失败'}

    def _analyze_linear_regression(self, df: pd.DataFrame) -> Dict:
        """线性回归趋势分析"""
        try:
            # 计算线性回归斜率
            linearreg_slope = talib.LINEARREG_SLOPE(df['close'], timeperiod=20)
            linearreg = talib.LINEARREG(df['close'], timeperiod=20)

            # R-squared计算 (简化版)
            y = df['close'].iloc[-20:].values
            x = np.arange(len(y))
            correlation = np.corrcoef(x, y)[0, 1]
            r_squared = correlation ** 2

            return {
                'slope': linearreg_slope.iloc[-1],
                'regression_line': linearreg.iloc[-1],
                'r_squared': r_squared,
                'trend_quality': 'HIGH' if r_squared > 0.7 else 'MEDIUM' if r_squared > 0.4 else 'LOW'
            }

        except Exception:
            return {'error': '线性回归分析失败'}

    def _detect_ema_crossovers(self, df: pd.DataFrame) -> List[Dict]:
        """检测EMA交叉信号"""
        crossovers = []

        try:
            # 计算常用EMA组合
            ema_pairs = [(8, 21), (12, 26), (21, 55)]

            for fast, slow in ema_pairs:
                if len(df) >= slow:
                    ema_fast = talib.EMA(df['close'], timeperiod=fast)
                    ema_slow = talib.EMA(df['close'], timeperiod=slow)

                    # 检测交叉
                    if len(ema_fast) >= 2:
                        current_diff = ema_fast.iloc[-1] - ema_slow.iloc[-1]
                        previous_diff = ema_fast.iloc[-2] - ema_slow.iloc[-2]

                        if previous_diff <= 0 and current_diff > 0:
                            crossovers.append({
                                'type': 'GOLDEN_CROSS',
                                'fast_ema': fast,
                                'slow_ema': slow,
                                'signal': 'BULLISH'
                            })
                        elif previous_diff >= 0 and current_diff < 0:
                            crossovers.append({
                                'type': 'DEATH_CROSS',
                                'fast_ema': fast,
                                'slow_ema': slow,
                                'signal': 'BEARISH'
                            })

            return crossovers

        except Exception:
            return []

    def _analyze_momentum_comprehensive(self, df: pd.DataFrame) -> Dict:
        """综合动量分析"""
        try:
            momentum_signals = []

            # 1. RSI分析 (包括多周期和变种)
            rsi_analysis = self._analyze_rsi_comprehensive(df)

            # 2. MACD分析
            macd_analysis = self._analyze_macd_comprehensive(df)

            # 3. 随机指标分析
            stochastic_analysis = self._analyze_stochastic(df)

            # 4. 威廉指标分析
            williams_analysis = self._analyze_williams_r(df)

            # 5. CCI指标分析
            cci_analysis = self._analyze_cci(df)

            # 6. 动量振荡器
            momentum_osc_analysis = self._analyze_momentum_oscillators(df)

            # 综合动量评分
            momentum_score = self._calculate_momentum_score(
                rsi_analysis, macd_analysis, stochastic_analysis,
                williams_analysis, cci_analysis, momentum_osc_analysis
            )

            return {
                'rsi_analysis': rsi_analysis,
                'macd_analysis': macd_analysis,
                'stochastic_analysis': stochastic_analysis,
                'williams_analysis': williams_analysis,
                'cci_analysis': cci_analysis,
                'momentum_oscillators': momentum_osc_analysis,
                'momentum_score': momentum_score,
                'overall_momentum': self._determine_overall_momentum(momentum_score)
            }

        except Exception as e:
            return {'error': f'动量分析失败: {str(e)}'}

    def _analyze_rsi_comprehensive(self, df: pd.DataFrame) -> Dict:
        """综合RSI分析"""
        rsi_data = {}
        rsi_signals = []

        try:
            # 多周期RSI
            for period in self.config['momentum_analysis']['rsi_periods']:
                if len(df) >= period:
                    rsi = simple_rsi(df['close'], period)
                    current_rsi = rsi.iloc[-1]
                    rsi_data[f'rsi_{period}'] = current_rsi

                    # RSI状态判断
                    overbought = self.config['momentum_analysis']['overbought_threshold']
                    oversold = self.config['momentum_analysis']['oversold_threshold']

                    if current_rsi > overbought:
                        state = "OVERBOUGHT"
                        signal = "BEARISH"
                    elif current_rsi < oversold:
                        state = "OVERSOLD"
                        signal = "BULLISH"
                    elif current_rsi > 50:
                        state = "BULLISH_MOMENTUM"
                        signal = "BULLISH"
                    else:
                        state = "BEARISH_MOMENTUM"
                        signal = "BEARISH"

                    rsi_signals.append({
                        'period': period,
                        'value': current_rsi,
                        'state': state,
                        'signal': signal
                    })

            # Stochastic RSI
            if len(df) >= 14:
                fastk, fastd = talib.STOCHRSI(df['close'], timeperiod=14, fastk_period=5, fastd_period=3)
                rsi_data['stoch_rsi_k'] = fastk.iloc[-1]
                rsi_data['stoch_rsi_d'] = fastd.iloc[-1]

            # RSI发散分析
            rsi_divergence = self._detect_rsi_divergence(df)

            return {
                'rsi_data': rsi_data,
                'rsi_signals': rsi_signals,
                'rsi_divergence': rsi_divergence
            }

        except Exception:
            return {'error': 'RSI分析失败'}

    def _analyze_macd_comprehensive(self, df: pd.DataFrame) -> Dict:
        """综合MACD分析"""
        macd_data = {}
        macd_signals = []

        try:
            # 多参数MACD
            for fast, slow, signal in self.config['momentum_analysis']['macd_params']:
                if len(df) >= slow:
                    macd, macd_signal, macd_hist = simple_macd(df['close'], fast, slow, signal)

                    param_key = f"{fast}_{slow}_{signal}"
                    macd_data[f'macd_{param_key}'] = macd.iloc[-1]
                    macd_data[f'signal_{param_key}'] = macd_signal.iloc[-1]
                    macd_data[f'histogram_{param_key}'] = macd_hist.iloc[-1]

                    # MACD信号判断
                    if macd.iloc[-1] > macd_signal.iloc[-1]:
                        crossover_signal = "BULLISH"
                    else:
                        crossover_signal = "BEARISH"

                    # 直方图趋势
                    if len(macd_hist) >= 2:
                        hist_trend = "INCREASING" if macd_hist.iloc[-1] > macd_hist.iloc[-2] else "DECREASING"
                    else:
                        hist_trend = "NEUTRAL"

                    macd_signals.append({
                        'params': param_key,
                        'crossover_signal': crossover_signal,
                        'histogram_trend': hist_trend,
                        'zero_line_position': "ABOVE" if macd.iloc[-1] > 0 else "BELOW"
                    })

            return {
                'macd_data': macd_data,
                'macd_signals': macd_signals
            }

        except Exception:
            return {'error': 'MACD分析失败'}

    def _analyze_stochastic(self, df: pd.DataFrame) -> Dict:
        """随机指标分析"""
        try:
            stoch_data = {}
            stoch_signals = []

            for k_period, k_slowing, d_period in self.config['momentum_analysis']['stoch_params']:
                if len(df) >= k_period:
                    slowk, slowd = talib.STOCH(
                        df['high'], df['low'], df['close'],
                        fastk_period=k_period, slowk_period=k_slowing, slowd_period=d_period
                    )

                    param_key = f"{k_period}_{k_slowing}_{d_period}"
                    stoch_data[f'k_{param_key}'] = slowk.iloc[-1]
                    stoch_data[f'd_{param_key}'] = slowd.iloc[-1]

                    # 随机指标状态
                    if slowk.iloc[-1] > 80:
                        state = "OVERBOUGHT"
                    elif slowk.iloc[-1] < 20:
                        state = "OVERSOLD"
                    else:
                        state = "NEUTRAL"

                    # K线和D线交叉
                    if slowk.iloc[-1] > slowd.iloc[-1]:
                        crossover = "BULLISH"
                    else:
                        crossover = "BEARISH"

                    stoch_signals.append({
                        'params': param_key,
                        'state': state,
                        'crossover': crossover
                    })

            return {
                'stoch_data': stoch_data,
                'stoch_signals': stoch_signals
            }

        except Exception:
            return {'error': '随机指标分析失败'}

    def _analyze_williams_r(self, df: pd.DataFrame) -> Dict:
        """威廉指标分析"""
        try:
            willr = talib.WILLR(df['high'], df['low'], df['close'], timeperiod=14)
            current_willr = willr.iloc[-1]

            # 威廉指标状态
            if current_willr > -20:
                state = "OVERBOUGHT"
                signal = "BEARISH"
            elif current_willr < -80:
                state = "OVERSOLD"
                signal = "BULLISH"
            else:
                state = "NEUTRAL"
                signal = "NEUTRAL"

            return {
                'williams_r': current_willr,
                'state': state,
                'signal': signal
            }

        except Exception:
            return {'error': '威廉指标分析失败'}

    def _analyze_cci(self, df: pd.DataFrame) -> Dict:
        """CCI指标分析"""
        try:
            cci = talib.CCI(df['high'], df['low'], df['close'], timeperiod=14)
            current_cci = cci.iloc[-1]

            # CCI状态判断
            if current_cci > 100:
                state = "OVERBOUGHT"
                signal = "BEARISH"
            elif current_cci < -100:
                state = "OVERSOLD"
                signal = "BULLISH"
            else:
                state = "NEUTRAL"
                signal = "NEUTRAL"

            return {
                'cci': current_cci,
                'state': state,
                'signal': signal
            }

        except Exception:
            return {'error': 'CCI分析失败'}

    def _analyze_momentum_oscillators(self, df: pd.DataFrame) -> Dict:
        """动量振荡器分析"""
        try:
            # ROC (Rate of Change)
            roc = talib.ROC(df['close'], timeperiod=10)

            # Momentum
            momentum = talib.MOM(df['close'], timeperiod=10)

            # Chande Momentum Oscillator
            cmo = talib.CMO(df['close'], timeperiod=14)

            return {
                'roc': roc.iloc[-1],
                'momentum': momentum.iloc[-1],
                'cmo': cmo.iloc[-1],
                'roc_signal': 'BULLISH' if roc.iloc[-1] > 0 else 'BEARISH',
                'momentum_signal': 'BULLISH' if momentum.iloc[-1] > 0 else 'BEARISH'
            }

        except Exception:
            return {'error': '动量振荡器分析失败'}

    def _detect_rsi_divergence(self, df: pd.DataFrame) -> Dict:
        """检测RSI发散"""
        try:
            if len(df) < 20:
                return {'divergence': 'NONE', 'reason': '数据不足'}

            rsi = talib.RSI(df['close'], timeperiod=14)

            # 简化的发散检测
            recent_price_high = df['close'].iloc[-10:].max()
            recent_price_low = df['close'].iloc[-10:].min()
            recent_rsi_high = rsi.iloc[-10:].max()
            recent_rsi_low = rsi.iloc[-10:].min()

            current_price = df['close'].iloc[-1]
            current_rsi = rsi.iloc[-1]

            # 看涨发散：价格创新低，RSI未创新低
            if current_price <= recent_price_low and current_rsi > recent_rsi_low:
                return {'divergence': 'BULLISH', 'strength': 'MODERATE'}

            # 看跌发散：价格创新高，RSI未创新高
            if current_price >= recent_price_high and current_rsi < recent_rsi_high:
                return {'divergence': 'BEARISH', 'strength': 'MODERATE'}

            return {'divergence': 'NONE'}

        except Exception:
            return {'divergence': 'NONE', 'error': 'RSI发散检测失败'}

    def _calculate_momentum_score(self, rsi_analysis, macd_analysis, stochastic_analysis,
                                williams_analysis, cci_analysis, momentum_osc_analysis) -> float:
        """计算综合动量评分"""
        try:
            score = 0.0
            total_weight = 0.0

            # RSI评分 (权重: 30%)
            if 'rsi_signals' in rsi_analysis:
                rsi_score = 0
                for signal in rsi_analysis['rsi_signals']:
                    if signal['signal'] == 'BULLISH':
                        rsi_score += 1
                    elif signal['signal'] == 'BEARISH':
                        rsi_score -= 1
                score += (rsi_score / len(rsi_analysis['rsi_signals'])) * 0.3
                total_weight += 0.3

            # MACD评分 (权重: 25%)
            if 'macd_signals' in macd_analysis:
                macd_score = 0
                for signal in macd_analysis['macd_signals']:
                    if signal['crossover_signal'] == 'BULLISH':
                        macd_score += 1
                    elif signal['crossover_signal'] == 'BEARISH':
                        macd_score -= 1
                score += (macd_score / len(macd_analysis['macd_signals'])) * 0.25
                total_weight += 0.25

            # 随机指标评分 (权重: 20%)
            if 'stoch_signals' in stochastic_analysis:
                stoch_score = 0
                for signal in stochastic_analysis['stoch_signals']:
                    if signal['crossover'] == 'BULLISH':
                        stoch_score += 1
                    elif signal['crossover'] == 'BEARISH':
                        stoch_score -= 1
                score += (stoch_score / len(stochastic_analysis['stoch_signals'])) * 0.20
                total_weight += 0.20

            # 威廉指标评分 (权重: 10%)
            if 'signal' in williams_analysis:
                if williams_analysis['signal'] == 'BULLISH':
                    score += 0.10
                elif williams_analysis['signal'] == 'BEARISH':
                    score -= 0.10
                total_weight += 0.10

            # CCI评分 (权重: 10%)
            if 'signal' in cci_analysis:
                if cci_analysis['signal'] == 'BULLISH':
                    score += 0.10
                elif cci_analysis['signal'] == 'BEARISH':
                    score -= 0.10
                total_weight += 0.10

            # 动量振荡器评分 (权重: 5%)
            if 'roc_signal' in momentum_osc_analysis:
                if momentum_osc_analysis['roc_signal'] == 'BULLISH':
                    score += 0.05
                elif momentum_osc_analysis['roc_signal'] == 'BEARISH':
                    score -= 0.05
                total_weight += 0.05

            return score / total_weight if total_weight > 0 else 0.0

        except Exception:
            return 0.0

    def _determine_overall_momentum(self, momentum_score: float) -> str:
        """确定整体动量方向"""
        if momentum_score > 0.3:
            return "STRONG_BULLISH"
        elif momentum_score > 0.1:
            return "BULLISH"
        elif momentum_score > -0.1:
            return "NEUTRAL"
        elif momentum_score > -0.3:
            return "BEARISH"
        else:
            return "STRONG_BEARISH"

    def _analyze_volatility_comprehensive(self, df: pd.DataFrame) -> Dict:
        """综合波动率分析"""
        try:
            # 1. 布林带分析
            bollinger_analysis = self._analyze_bollinger_bands(df)

            # 2. ATR分析
            atr_analysis = self._analyze_atr(df)

            # 3. Keltner通道分析
            keltner_analysis = self._analyze_keltner_channels(df)

            # 4. 波动率突破分析
            volatility_breakout = self._analyze_volatility_breakout(df)

            # 5. 历史波动率分析
            historical_volatility = self._calculate_historical_volatility(df)

            # 综合波动率评分
            volatility_score = self._calculate_volatility_score(
                bollinger_analysis, atr_analysis, keltner_analysis, volatility_breakout
            )

            return {
                'bollinger_analysis': bollinger_analysis,
                'atr_analysis': atr_analysis,
                'keltner_analysis': keltner_analysis,
                'volatility_breakout': volatility_breakout,
                'historical_volatility': historical_volatility,
                'volatility_score': volatility_score,
                'volatility_regime': self._determine_volatility_regime(volatility_score)
            }

        except Exception as e:
            return {'error': f'波动率分析失败: {str(e)}'}

    def _analyze_bollinger_bands(self, df: pd.DataFrame) -> Dict:
        """布林带分析"""
        try:
            bb_data = {}
            bb_signals = []

            for period in self.config['volatility_analysis']['bb_periods']:
                if len(df) >= period:
                    bb_upper, bb_middle, bb_lower = talib.BBANDS(
                        df['close'], timeperiod=period,
                        nbdevup=self.config['volatility_analysis']['bb_std_dev'],
                        nbdevdn=self.config['volatility_analysis']['bb_std_dev']
                    )

                    current_price = df['close'].iloc[-1]
                    bb_data[f'bb_upper_{period}'] = bb_upper.iloc[-1]
                    bb_data[f'bb_middle_{period}'] = bb_middle.iloc[-1]
                    bb_data[f'bb_lower_{period}'] = bb_lower.iloc[-1]

                    # 布林带位置
                    bb_position = (current_price - bb_lower.iloc[-1]) / (bb_upper.iloc[-1] - bb_lower.iloc[-1])
                    bb_data[f'bb_position_{period}'] = bb_position

                    # 布林带宽度
                    bb_width = (bb_upper.iloc[-1] - bb_lower.iloc[-1]) / bb_middle.iloc[-1]
                    bb_data[f'bb_width_{period}'] = bb_width

                    # 布林带信号
                    if current_price > bb_upper.iloc[-1]:
                        signal = "OVERBOUGHT"
                    elif current_price < bb_lower.iloc[-1]:
                        signal = "OVERSOLD"
                    elif bb_position > 0.8:
                        signal = "APPROACHING_UPPER"
                    elif bb_position < 0.2:
                        signal = "APPROACHING_LOWER"
                    else:
                        signal = "NEUTRAL"

                    bb_signals.append({
                        'period': period,
                        'position': bb_position,
                        'width': bb_width,
                        'signal': signal
                    })

            return {
                'bb_data': bb_data,
                'bb_signals': bb_signals
            }

        except Exception:
            return {'error': '布林带分析失败'}
