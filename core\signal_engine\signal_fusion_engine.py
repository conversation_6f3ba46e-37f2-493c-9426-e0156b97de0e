"""
QuantumForex Pro - 信号融合引擎
多因子信号智能融合，生成高质量交易信号
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json
from datetime import datetime

from config.config import config

class SignalType(Enum):
    """信号类型枚举"""
    TECHNICAL = "technical"
    FUNDAMENTAL = "fundamental"
    SENTIMENT = "sentiment"
    MICROSTRUCTURE = "microstructure"

class SignalDirection(Enum):
    """信号方向枚举"""
    STRONG_BUY = 2
    BUY = 1
    NEUTRAL = 0
    SELL = -1
    STRONG_SELL = -2

@dataclass
class FusedSignal:
    """融合信号数据结构"""
    symbol: str
    direction: SignalDirection
    strength: float  # 0-1
    confidence: float  # 0-1
    entry_price: float
    stop_loss: float
    take_profit: float
    risk_reward_ratio: float
    timeframe: str
    timestamp: str
    contributing_signals: List[Dict]
    signal_quality: str

class SignalFusionEngine:
    """
    信号融合引擎
    智能融合多维度信号，生成高质量交易决策
    """

    def __init__(self):
        self.config = config
        self.signal_weights = self._initialize_signal_weights()
        self.fusion_history = []

    def _initialize_signal_weights(self) -> Dict:
        """初始化信号权重"""
        return {
            'trend_analysis': 0.40,      # 趋势分析权重40%
            'momentum_analysis': 0.30,   # 动量分析权重30%
            'volatility_analysis': 0.20, # 波动率分析权重20%
            'volume_analysis': 0.10,     # 成交量分析权重10%

            # 子权重
            'technical_indicators': 0.70,  # 技术指标权重70%
            'pattern_recognition': 0.20,   # 模式识别权重20%
            'microstructure': 0.10        # 微观结构权重10%
        }

    def fuse_signals(self, technical_analysis: Dict, symbol: str, timeframe: str = '15min') -> FusedSignal:
        """
        融合多维度信号

        Args:
            technical_analysis: 技术分析结果
            symbol: 交易品种
            timeframe: 时间框架

        Returns:
            FusedSignal: 融合后的信号
        """
        try:
            # 1. 提取各维度信号
            trend_signal = self._extract_trend_signal(technical_analysis.get('trend_analysis', {}))
            momentum_signal = self._extract_momentum_signal(technical_analysis.get('momentum_analysis', {}))
            volatility_signal = self._extract_volatility_signal(technical_analysis.get('volatility_analysis', {}))
            volume_signal = self._extract_volume_signal(technical_analysis.get('volume_analysis', {}))

            # 2. 计算加权信号强度
            weighted_signals = self._calculate_weighted_signals(
                trend_signal, momentum_signal, volatility_signal, volume_signal
            )

            # 3. 确定综合信号方向
            signal_direction = self._determine_signal_direction(weighted_signals)

            # 4. 计算信号强度和置信度
            signal_strength = self._calculate_signal_strength(weighted_signals)
            signal_confidence = self._calculate_signal_confidence(weighted_signals, technical_analysis)

            # 5. 计算入场价格和风险管理参数
            entry_params = self._calculate_entry_parameters(
                technical_analysis, signal_direction, signal_strength
            )

            # 6. 评估信号质量
            signal_quality = self._assess_signal_quality(
                signal_strength, signal_confidence, weighted_signals
            )

            # 7. 创建融合信号
            fused_signal = FusedSignal(
                symbol=symbol,
                direction=signal_direction,
                strength=signal_strength,
                confidence=signal_confidence,
                entry_price=entry_params['entry_price'],
                stop_loss=entry_params['stop_loss'],
                take_profit=entry_params['take_profit'],
                risk_reward_ratio=entry_params['risk_reward_ratio'],
                timeframe=timeframe,
                timestamp=datetime.now().isoformat(),
                contributing_signals=self._get_contributing_signals(
                    trend_signal, momentum_signal, volatility_signal, volume_signal
                ),
                signal_quality=signal_quality
            )

            # 8. 记录融合历史
            self._record_fusion_history(fused_signal, technical_analysis)

            return fused_signal

        except Exception as e:
            # 返回中性信号
            print(f"⚠️ 信号融合失败: {e}")
            return self._create_neutral_signal(symbol, timeframe, str(e))

    def _create_neutral_signal(self, symbol: str, timeframe: str, error: str = "") -> FusedSignal:
        """创建中性信号"""
        return FusedSignal(
            symbol=symbol,
            direction=SignalDirection.NEUTRAL,
            strength=0.0,
            confidence=0.0,
            entry_price=0.0,
            stop_loss=0.0,
            take_profit=0.0,
            risk_reward_ratio=0.0,
            timeframe=timeframe,
            timestamp=datetime.now().isoformat(),
            contributing_signals=[],
            signal_quality="LOW"
        )

    def _extract_trend_signal(self, trend_analysis: Dict) -> Dict:
        """提取趋势信号"""
        try:
            if 'error' in trend_analysis:
                return {'direction': 0, 'strength': 0, 'confidence': 0}

            trend_score = trend_analysis.get('trend_score', 0)
            overall_trend = trend_analysis.get('overall_trend', 'NEUTRAL')

            # 转换趋势方向为数值
            direction_map = {
                'STRONG_BULLISH': 2,
                'BULLISH': 1,
                'NEUTRAL': 0,
                'BEARISH': -1,
                'STRONG_BEARISH': -2
            }

            direction = direction_map.get(overall_trend, 0)
            strength = min(abs(trend_score), 1.0)  # 限制在0-1范围

            # 基于ADX计算置信度
            adx_analysis = trend_analysis.get('adx_analysis', {})
            if 'adx' in adx_analysis:
                adx_value = adx_analysis['adx']
                confidence = min(adx_value / 50.0, 1.0)  # ADX越高置信度越高
            else:
                confidence = strength

            return {
                'direction': direction,
                'strength': strength,
                'confidence': confidence,
                'details': {
                    'trend_score': trend_score,
                    'overall_trend': overall_trend,
                    'adx_strength': adx_analysis.get('trend_strength', 'UNKNOWN')
                }
            }

        except Exception:
            return {'direction': 0, 'strength': 0, 'confidence': 0}

    def _extract_momentum_signal(self, momentum_analysis: Dict) -> Dict:
        """提取动量信号"""
        try:
            if 'error' in momentum_analysis:
                return {'direction': 0, 'strength': 0, 'confidence': 0}

            momentum_score = momentum_analysis.get('momentum_score', 0)
            overall_momentum = momentum_analysis.get('overall_momentum', 'NEUTRAL')

            # 转换动量方向为数值
            direction_map = {
                'STRONG_BULLISH': 2,
                'BULLISH': 1,
                'NEUTRAL': 0,
                'BEARISH': -1,
                'STRONG_BEARISH': -2
            }

            direction = direction_map.get(overall_momentum, 0)
            strength = min(abs(momentum_score), 1.0)

            # 基于RSI发散计算置信度
            rsi_analysis = momentum_analysis.get('rsi_analysis', {})
            rsi_divergence = rsi_analysis.get('rsi_divergence', {})

            if rsi_divergence.get('divergence') != 'NONE':
                confidence = min(strength + 0.2, 1.0)  # 发散增加置信度
            else:
                confidence = strength

            return {
                'direction': direction,
                'strength': strength,
                'confidence': confidence,
                'details': {
                    'momentum_score': momentum_score,
                    'overall_momentum': overall_momentum,
                    'rsi_divergence': rsi_divergence.get('divergence', 'NONE')
                }
            }

        except Exception:
            return {'direction': 0, 'strength': 0, 'confidence': 0}

    def _extract_volatility_signal(self, volatility_analysis: Dict) -> Dict:
        """提取波动率信号"""
        try:
            if 'error' in volatility_analysis:
                return {'direction': 0, 'strength': 0, 'confidence': 0}

            volatility_score = volatility_analysis.get('volatility_score', 0)
            volatility_regime = volatility_analysis.get('volatility_regime', 'NORMAL')

            # 波动率主要影响信号强度而非方向
            direction = 0  # 波动率本身不提供方向信号

            # 根据波动率状态调整强度
            regime_strength_map = {
                'LOW': 0.3,      # 低波动率，信号强度降低
                'NORMAL': 0.7,   # 正常波动率
                'HIGH': 0.5,     # 高波动率，信号可靠性降低
                'EXTREME': 0.2   # 极端波动率，避免交易
            }

            strength = regime_strength_map.get(volatility_regime, 0.5)
            confidence = strength

            return {
                'direction': direction,
                'strength': strength,
                'confidence': confidence,
                'details': {
                    'volatility_score': volatility_score,
                    'volatility_regime': volatility_regime
                }
            }

        except Exception:
            return {'direction': 0, 'strength': 0, 'confidence': 0}

    def _extract_volume_signal(self, volume_analysis: Dict) -> Dict:
        """提取成交量信号"""
        try:
            if 'error' in volume_analysis:
                return {'direction': 0, 'strength': 0, 'confidence': 0}

            # 成交量主要用于确认其他信号
            volume_trend = volume_analysis.get('volume_trend', 'NEUTRAL')
            volume_strength = volume_analysis.get('volume_strength', 0.5)

            # 成交量确认信号
            if volume_trend == 'INCREASING':
                direction = 0  # 成交量增加，确认当前趋势
                strength = min(volume_strength, 1.0)
            elif volume_trend == 'DECREASING':
                direction = 0  # 成交量减少，信号减弱
                strength = max(volume_strength * 0.5, 0.1)
            else:
                direction = 0
                strength = 0.5

            confidence = strength

            return {
                'direction': direction,
                'strength': strength,
                'confidence': confidence,
                'details': {
                    'volume_trend': volume_trend,
                    'volume_strength': volume_strength
                }
            }

        except Exception:
            return {'direction': 0, 'strength': 0, 'confidence': 0}

    def _calculate_weighted_signals(self, trend_signal: Dict, momentum_signal: Dict,
                                  volatility_signal: Dict, volume_signal: Dict) -> Dict:
        """计算加权信号"""
        try:
            weights = self.signal_weights

            # 计算加权方向
            weighted_direction = (
                trend_signal['direction'] * weights['trend_analysis'] +
                momentum_signal['direction'] * weights['momentum_analysis']
            )

            # 计算加权强度
            weighted_strength = (
                trend_signal['strength'] * weights['trend_analysis'] +
                momentum_signal['strength'] * weights['momentum_analysis'] +
                volatility_signal['strength'] * weights['volatility_analysis'] +
                volume_signal['strength'] * weights['volume_analysis']
            )

            # 计算加权置信度
            weighted_confidence = (
                trend_signal['confidence'] * weights['trend_analysis'] +
                momentum_signal['confidence'] * weights['momentum_analysis'] +
                volatility_signal['confidence'] * weights['volatility_analysis'] +
                volume_signal['confidence'] * weights['volume_analysis']
            )

            return {
                'direction': weighted_direction,
                'strength': weighted_strength,
                'confidence': weighted_confidence,
                'components': {
                    'trend': trend_signal,
                    'momentum': momentum_signal,
                    'volatility': volatility_signal,
                    'volume': volume_signal
                }
            }

        except Exception:
            return {'direction': 0, 'strength': 0, 'confidence': 0, 'components': {}}

    def _determine_signal_direction(self, weighted_signals: Dict) -> SignalDirection:
        """确定信号方向"""
        direction_value = weighted_signals.get('direction', 0)
        strength = weighted_signals.get('strength', 0)

        # 降低强度阈值，更容易产生信号
        if strength < 0.2:
            return SignalDirection.NEUTRAL

        if direction_value > 0.8:
            return SignalDirection.STRONG_BUY
        elif direction_value > 0.2:  # 降低阈值
            return SignalDirection.BUY
        elif direction_value < -0.8:
            return SignalDirection.STRONG_SELL
        elif direction_value < -0.2:  # 降低阈值
            return SignalDirection.SELL
        else:
            return SignalDirection.NEUTRAL

    def _calculate_signal_strength(self, weighted_signals: Dict) -> float:
        """计算信号强度"""
        base_strength = weighted_signals.get('strength', 0)
        confidence = weighted_signals.get('confidence', 0)

        # 综合强度和置信度
        final_strength = (base_strength * 0.7 + confidence * 0.3)

        return min(max(final_strength, 0.0), 1.0)

    def _calculate_signal_confidence(self, weighted_signals: Dict, technical_analysis: Dict) -> float:
        """计算信号置信度"""
        base_confidence = weighted_signals.get('confidence', 0)

        # 基于信号一致性调整置信度
        components = weighted_signals.get('components', {})

        # 检查趋势和动量是否一致
        trend_dir = components.get('trend', {}).get('direction', 0)
        momentum_dir = components.get('momentum', {}).get('direction', 0)

        if (trend_dir > 0 and momentum_dir > 0) or (trend_dir < 0 and momentum_dir < 0):
            consistency_bonus = 0.2  # 一致性奖励
        else:
            consistency_bonus = -0.1  # 不一致性惩罚

        final_confidence = base_confidence + consistency_bonus

        return min(max(final_confidence, 0.0), 1.0)

    def _calculate_entry_parameters(self, technical_analysis: Dict, signal_direction, signal_strength: float) -> Dict:
        """计算入场参数 - 使用真实市场价格"""
        try:
            # 从技术分析中获取当前价格
            symbol = technical_analysis.get('symbol', 'UNKNOWN')

            # 尝试从技术分析数据中获取当前价格
            if 'trend_analysis' in technical_analysis and 'sma_20' in technical_analysis['trend_analysis']:
                # 使用SMA20作为参考价格
                entry_price = technical_analysis['trend_analysis']['sma_20']
            elif 'data_points' in technical_analysis and technical_analysis['data_points'] > 0:
                # 使用默认价格，但根据货币对调整
                currency_prices = {
                    'EURUSD': 1.1379,
                    'GBPUSD': 1.3558,
                    'AUDUSD': 0.6505,
                    'USDCHF': 0.8225,
                    'USDCAD': 1.3500,
                    'USDJPY': 150.00,
                    'NZDUSD': 0.6000,
                    'GOLD': 2000.00
                }
                entry_price = currency_prices.get(symbol, 1.1000)
            else:
                entry_price = 1.1000

            # 根据信号强度调整风险参数
            base_risk = 0.002  # 基础风险0.2%
            risk_multiplier = max(0.5, min(2.0, signal_strength))  # 0.5-2.0倍
            actual_risk = base_risk * risk_multiplier

            # 根据货币对调整点值
            pip_values = {
                'EURUSD': 0.0001,
                'GBPUSD': 0.0001,
                'AUDUSD': 0.0001,
                'USDCHF': 0.0001,
                'USDCAD': 0.0001,
                'USDJPY': 0.01,
                'NZDUSD': 0.0001,
                'GOLD': 0.1
            }
            pip_value = pip_values.get(symbol, 0.0001)

            if signal_direction.value > 0:  # 买入信号
                stop_loss = entry_price * (1 - actual_risk)
                take_profit = entry_price * (1 + actual_risk * 3)  # 1:3风险回报比
            elif signal_direction.value < 0:  # 卖出信号
                stop_loss = entry_price * (1 + actual_risk)
                take_profit = entry_price * (1 - actual_risk * 3)  # 1:3风险回报比
            else:  # 中性信号
                stop_loss = entry_price
                take_profit = entry_price

            # 计算风险回报比
            if stop_loss != entry_price:
                risk_reward_ratio = abs(take_profit - entry_price) / abs(entry_price - stop_loss)
            else:
                risk_reward_ratio = 0.0

            return {
                'entry_price': round(entry_price, 5),
                'stop_loss': round(stop_loss, 5),
                'take_profit': round(take_profit, 5),
                'risk_reward_ratio': round(risk_reward_ratio, 2)
            }

        except Exception as e:
            print(f"⚠️ 入场参数计算失败: {e}")
            return {
                'entry_price': 1.1000,
                'stop_loss': 1.1000,
                'take_profit': 1.1000,
                'risk_reward_ratio': 0.0
            }

    def _assess_signal_quality(self, signal_strength: float, signal_confidence: float, weighted_signals: Dict) -> str:
        """评估信号质量"""
        try:
            # 综合评分
            quality_score = (signal_strength * 0.6 + signal_confidence * 0.4)

            if quality_score > 0.8:
                return "HIGH"
            elif quality_score > 0.6:
                return "MEDIUM"
            elif quality_score > 0.4:
                return "LOW"
            else:
                return "VERY_LOW"

        except Exception:
            return "LOW"

    def _get_contributing_signals(self, trend_signal: Dict, momentum_signal: Dict,
                                volatility_signal: Dict, volume_signal: Dict) -> List[Dict]:
        """获取贡献信号列表"""
        try:
            signals = []

            if trend_signal.get('direction', 0) != 0:
                signals.append({
                    'type': 'trend',
                    'direction': trend_signal['direction'],
                    'strength': trend_signal.get('strength', 0),
                    'confidence': trend_signal.get('confidence', 0)
                })

            if momentum_signal.get('direction', 0) != 0:
                signals.append({
                    'type': 'momentum',
                    'direction': momentum_signal['direction'],
                    'strength': momentum_signal.get('strength', 0),
                    'confidence': momentum_signal.get('confidence', 0)
                })

            return signals

        except Exception:
            return []

    def _record_fusion_history(self, fused_signal: FusedSignal, technical_analysis: Dict):
        """记录融合历史"""
        try:
            history_record = {
                'timestamp': fused_signal.timestamp,
                'symbol': fused_signal.symbol,
                'direction': fused_signal.direction.name,
                'strength': fused_signal.strength,
                'confidence': fused_signal.confidence,
                'signal_quality': fused_signal.signal_quality
            }

            self.fusion_history.append(history_record)

            # 保持历史记录在合理范围内
            if len(self.fusion_history) > 1000:
                self.fusion_history = self.fusion_history[-500:]

        except Exception:
            pass
