"""
主策略引擎
整合多种专业交易策略，实现智能策略选择和执行
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

from .professional_portfolio_strategy import ProfessionalPortfolioStrategy, PortfolioDecision, PortfolioAction

class StrategyType(Enum):
    PORTFOLIO_STRATEGY = "portfolio_strategy"
    MOMENTUM_STRATEGY = "momentum_strategy"
    MEAN_REVERSION = "mean_reversion"
    BREAKOUT_STRATEGY = "breakout_strategy"
    CARRY_TRADE = "carry_trade"

class MarketCondition(Enum):
    TRENDING = "trending"
    RANGING = "ranging"
    VOLATILE = "volatile"
    CALM = "calm"
    UNCERTAIN = "uncertain"

@dataclass
class StrategyPerformance:
    strategy_type: StrategyType
    win_rate: float
    avg_return: float
    max_drawdown: float
    sharpe_ratio: float
    recent_performance: float

@dataclass
class MasterDecision:
    strategy_used: StrategyType
    decisions: List[PortfolioDecision]
    confidence: float
    market_condition: MarketCondition
    reasoning: str

class MasterStrategyEngine:
    """
    主策略引擎
    
    核心功能：
    1. 智能识别市场条件
    2. 动态选择最适合的策略
    3. 多策略组合优化
    4. 实时策略性能评估
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 初始化各种策略
        self.portfolio_strategy = ProfessionalPortfolioStrategy()
        
        # 策略性能跟踪
        self.strategy_performance = {
            StrategyType.PORTFOLIO_STRATEGY: StrategyPerformance(
                strategy_type=StrategyType.PORTFOLIO_STRATEGY,
                win_rate=0.65,
                avg_return=0.02,
                max_drawdown=0.05,
                sharpe_ratio=1.2,
                recent_performance=0.0
            )
        }
        
        # 市场条件检测参数
        self.market_detection_params = {
            'trend_threshold': 0.02,
            'volatility_threshold': 0.15,
            'volume_threshold': 1.5,
            'correlation_threshold': 0.7
        }
        
        # 策略选择权重
        self.strategy_weights = {
            StrategyType.PORTFOLIO_STRATEGY: 1.0
        }
        
        # 历史决策记录
        self.decision_history = []
        self.performance_history = []
        
    def generate_master_decision(self, market_data: Dict, current_positions: Dict) -> MasterDecision:
        """生成主策略决策"""
        try:
            # 1. 识别市场条件
            market_condition = self._identify_market_condition(market_data)
            self.logger.info(f"识别市场条件: {market_condition.value}")
            
            # 2. 选择最适合的策略
            selected_strategy = self._select_optimal_strategy(market_condition, market_data)
            self.logger.info(f"选择策略: {selected_strategy.value}")
            
            # 3. 执行选定策略
            decisions = self._execute_strategy(selected_strategy, market_data, current_positions)
            
            # 4. 计算整体置信度
            overall_confidence = self._calculate_overall_confidence(decisions, market_condition)
            
            # 5. 生成决策说明
            reasoning = self._generate_reasoning(selected_strategy, market_condition, decisions)
            
            # 6. 创建主决策
            master_decision = MasterDecision(
                strategy_used=selected_strategy,
                decisions=decisions,
                confidence=overall_confidence,
                market_condition=market_condition,
                reasoning=reasoning
            )
            
            # 7. 记录决策历史
            self._record_decision(master_decision)
            
            return master_decision
            
        except Exception as e:
            self.logger.error(f"生成主策略决策失败: {e}")
            return self._create_safe_decision(market_data)
    
    def _identify_market_condition(self, market_data: Dict) -> MarketCondition:
        """识别市场条件"""
        try:
            if not market_data:
                return MarketCondition.UNCERTAIN
                
            # 分析主要货币对的市场特征
            trend_scores = []
            volatility_scores = []
            volume_scores = []
            
            for symbol, data in market_data.items():
                if 'ohlcv' not in data or data['ohlcv'].empty:
                    continue
                    
                df = data['ohlcv']
                
                # 趋势分析
                trend_score = self._calculate_trend_score(df)
                trend_scores.append(trend_score)
                
                # 波动率分析
                volatility_score = self._calculate_volatility_score(df)
                volatility_scores.append(volatility_score)
                
                # 成交量分析
                volume_score = self._calculate_volume_score(df)
                volume_scores.append(volume_score)
            
            if not trend_scores:
                return MarketCondition.UNCERTAIN
                
            # 综合评估
            avg_trend = np.mean(trend_scores)
            avg_volatility = np.mean(volatility_scores)
            avg_volume = np.mean(volume_scores)
            
            # 判断市场条件
            if abs(avg_trend) > self.market_detection_params['trend_threshold']:
                if avg_volatility > self.market_detection_params['volatility_threshold']:
                    return MarketCondition.VOLATILE
                else:
                    return MarketCondition.TRENDING
            else:
                if avg_volatility > self.market_detection_params['volatility_threshold']:
                    return MarketCondition.VOLATILE
                elif avg_volatility < self.market_detection_params['volatility_threshold'] * 0.5:
                    return MarketCondition.CALM
                else:
                    return MarketCondition.RANGING
                    
        except Exception as e:
            self.logger.error(f"识别市场条件失败: {e}")
            return MarketCondition.UNCERTAIN
    
    def _calculate_trend_score(self, df: pd.DataFrame) -> float:
        """计算趋势评分"""
        if len(df) < 50:
            return 0.0
            
        # 使用多个时间框架的移动平均线
        sma_20 = df['close'].rolling(20).mean()
        sma_50 = df['close'].rolling(50).mean()
        
        if sma_20.isna().all() or sma_50.isna().all():
            return 0.0
            
        # 计算趋势强度
        trend_strength = (sma_20.iloc[-1] - sma_50.iloc[-1]) / sma_50.iloc[-1]
        
        return max(-1.0, min(1.0, trend_strength * 10))  # 放大并限制范围
    
    def _calculate_volatility_score(self, df: pd.DataFrame) -> float:
        """计算波动率评分"""
        if len(df) < 20:
            return 0.0
            
        returns = df['close'].pct_change().dropna()
        if returns.empty:
            return 0.0
            
        volatility = returns.rolling(20).std().iloc[-1] * np.sqrt(252)
        return volatility
    
    def _calculate_volume_score(self, df: pd.DataFrame) -> float:
        """计算成交量评分"""
        if 'volume' not in df.columns or len(df) < 20:
            return 1.0  # 默认值
            
        avg_volume = df['volume'].rolling(20).mean()
        if avg_volume.isna().all():
            return 1.0
            
        current_volume = df['volume'].iloc[-1]
        volume_ratio = current_volume / avg_volume.iloc[-1] if avg_volume.iloc[-1] > 0 else 1.0
        
        return volume_ratio
    
    def _select_optimal_strategy(self, market_condition: MarketCondition, market_data: Dict) -> StrategyType:
        """选择最优策略"""
        try:
            # 基于市场条件和策略性能选择策略
            strategy_scores = {}
            
            for strategy_type, performance in self.strategy_performance.items():
                base_score = performance.sharpe_ratio * performance.win_rate
                
                # 根据市场条件调整评分
                condition_multiplier = self._get_condition_multiplier(strategy_type, market_condition)
                
                # 考虑最近表现
                recent_multiplier = 1.0 + performance.recent_performance
                
                final_score = base_score * condition_multiplier * recent_multiplier
                strategy_scores[strategy_type] = final_score
            
            # 选择评分最高的策略
            best_strategy = max(strategy_scores.items(), key=lambda x: x[1])[0]
            
            self.logger.info(f"策略评分: {strategy_scores}")
            return best_strategy
            
        except Exception as e:
            self.logger.error(f"选择策略失败: {e}")
            return StrategyType.PORTFOLIO_STRATEGY  # 默认策略
    
    def _get_condition_multiplier(self, strategy_type: StrategyType, condition: MarketCondition) -> float:
        """获取市场条件对策略的影响系数"""
        multipliers = {
            StrategyType.PORTFOLIO_STRATEGY: {
                MarketCondition.TRENDING: 1.2,
                MarketCondition.RANGING: 1.0,
                MarketCondition.VOLATILE: 0.8,
                MarketCondition.CALM: 1.1,
                MarketCondition.UNCERTAIN: 0.9
            }
        }
        
        return multipliers.get(strategy_type, {}).get(condition, 1.0)
    
    def _execute_strategy(self, strategy_type: StrategyType, market_data: Dict, current_positions: Dict) -> List[PortfolioDecision]:
        """执行选定的策略"""
        try:
            if strategy_type == StrategyType.PORTFOLIO_STRATEGY:
                return self.portfolio_strategy.generate_portfolio_decisions(market_data, current_positions)
            else:
                # 其他策略的实现
                self.logger.warning(f"策略 {strategy_type.value} 尚未实现")
                return []
                
        except Exception as e:
            self.logger.error(f"执行策略 {strategy_type.value} 失败: {e}")
            return []
    
    def _calculate_overall_confidence(self, decisions: List[PortfolioDecision], market_condition: MarketCondition) -> float:
        """计算整体置信度"""
        if not decisions:
            return 0.0
            
        # 基于决策的平均置信度
        avg_confidence = np.mean([d.confidence for d in decisions])
        
        # 根据市场条件调整
        condition_adjustment = {
            MarketCondition.TRENDING: 1.1,
            MarketCondition.RANGING: 1.0,
            MarketCondition.VOLATILE: 0.8,
            MarketCondition.CALM: 1.05,
            MarketCondition.UNCERTAIN: 0.7
        }.get(market_condition, 1.0)
        
        # 考虑决策数量（太多决策可能降低置信度）
        quantity_adjustment = max(0.7, 1.0 - (len(decisions) - 1) * 0.1)
        
        final_confidence = avg_confidence * condition_adjustment * quantity_adjustment
        return max(0.0, min(1.0, final_confidence))
    
    def _generate_reasoning(self, strategy_type: StrategyType, market_condition: MarketCondition, decisions: List[PortfolioDecision]) -> str:
        """生成决策说明"""
        reasoning_parts = [
            f"市场条件: {market_condition.value}",
            f"选择策略: {strategy_type.value}",
            f"生成决策数量: {len(decisions)}"
        ]
        
        if decisions:
            action_counts = {}
            for decision in decisions:
                action = decision.action.value
                action_counts[action] = action_counts.get(action, 0) + 1
            
            action_summary = ", ".join([f"{action}: {count}" for action, count in action_counts.items()])
            reasoning_parts.append(f"决策分布: {action_summary}")
            
            avg_confidence = np.mean([d.confidence for d in decisions])
            reasoning_parts.append(f"平均置信度: {avg_confidence:.2f}")
        
        return " | ".join(reasoning_parts)
    
    def _record_decision(self, decision: MasterDecision):
        """记录决策历史"""
        self.decision_history.append({
            'timestamp': datetime.now(),
            'strategy': decision.strategy_used.value,
            'market_condition': decision.market_condition.value,
            'decisions_count': len(decision.decisions),
            'confidence': decision.confidence,
            'reasoning': decision.reasoning
        })
        
        # 保持历史记录在合理范围内
        if len(self.decision_history) > 1000:
            self.decision_history = self.decision_history[-500:]
    
    def _create_safe_decision(self, market_data: Dict) -> MasterDecision:
        """创建安全的默认决策"""
        return MasterDecision(
            strategy_used=StrategyType.PORTFOLIO_STRATEGY,
            decisions=[],
            confidence=0.0,
            market_condition=MarketCondition.UNCERTAIN,
            reasoning="系统异常，返回安全决策"
        )
    
    def update_strategy_performance(self, strategy_type: StrategyType, performance_metrics: Dict):
        """更新策略性能"""
        try:
            if strategy_type in self.strategy_performance:
                perf = self.strategy_performance[strategy_type]
                
                # 更新性能指标
                perf.win_rate = performance_metrics.get('win_rate', perf.win_rate)
                perf.avg_return = performance_metrics.get('avg_return', perf.avg_return)
                perf.max_drawdown = performance_metrics.get('max_drawdown', perf.max_drawdown)
                perf.sharpe_ratio = performance_metrics.get('sharpe_ratio', perf.sharpe_ratio)
                perf.recent_performance = performance_metrics.get('recent_performance', perf.recent_performance)
                
                self.logger.info(f"更新策略 {strategy_type.value} 性能: {performance_metrics}")
                
        except Exception as e:
            self.logger.error(f"更新策略性能失败: {e}")
    
    def get_strategy_statistics(self) -> Dict:
        """获取策略统计信息"""
        return {
            'strategy_performance': {k.value: {
                'win_rate': v.win_rate,
                'avg_return': v.avg_return,
                'max_drawdown': v.max_drawdown,
                'sharpe_ratio': v.sharpe_ratio,
                'recent_performance': v.recent_performance
            } for k, v in self.strategy_performance.items()},
            'decision_history_count': len(self.decision_history),
            'recent_decisions': self.decision_history[-10:] if self.decision_history else []
        }
