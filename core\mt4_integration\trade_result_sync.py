#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
交易结果同步器
将MT4的真实交易结果同步到学习系统
"""

import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

from .mt4_order_tracker import MT4OrderTracker, MT4Order
from ..learning_system import LearningCoordinator

@dataclass
class TradeMapping:
    """交易映射关系"""
    system_trade_id: str
    mt4_ticket: int
    symbol: str
    action: str
    volume: float
    system_entry_price: float
    mt4_entry_price: float
    created_at: datetime
    status: str = "PENDING"  # PENDING/MATCHED/CLOSED

class TradeResultSync:
    """交易结果同步器"""
    
    def __init__(self, learning_coordinator: LearningCoordinator):
        self.learning_coordinator = learning_coordinator
        self.logger = logging.getLogger(__name__)
        
        # MT4订单跟踪器
        self.mt4_tracker = MT4OrderTracker()
        
        # 交易映射
        self.trade_mappings = {}  # system_trade_id -> TradeMapping
        self.pending_mappings = []  # 等待匹配的系统交易
        
        # 同步状态
        self.sync_enabled = True
        self.sync_thread = None
        self.running = False
        
        # 匹配参数
        self.price_tolerance = 0.0005  # 价格容忍度
        self.time_tolerance = 300  # 时间容忍度（秒）
        
        # 注册MT4订单回调
        self.mt4_tracker.register_order_opened_callback(self._on_mt4_order_opened)
        self.mt4_tracker.register_order_closed_callback(self._on_mt4_order_closed)
        
    def start(self):
        """启动同步服务"""
        try:
            if self.running:
                self.logger.warning("交易结果同步已经在运行")
                return
            
            # 启动MT4订单跟踪
            self.mt4_tracker.start_monitoring()
            
            # 启动同步线程
            self.running = True
            self.sync_thread = threading.Thread(target=self._sync_loop, daemon=True)
            self.sync_thread.start()
            
            self.logger.info("交易结果同步启动成功")
            
        except Exception as e:
            self.logger.error(f"启动交易结果同步失败: {e}")
            self.running = False
    
    def stop(self):
        """停止同步服务"""
        try:
            self.running = False
            
            # 停止MT4订单跟踪
            self.mt4_tracker.stop_monitoring()
            
            # 停止同步线程
            if self.sync_thread and self.sync_thread.is_alive():
                self.sync_thread.join(timeout=10)
            
            self.logger.info("交易结果同步已停止")
            
        except Exception as e:
            self.logger.error(f"停止交易结果同步失败: {e}")
    
    def register_system_trade(self, trade_id: str, trade_data: Dict) -> bool:
        """注册系统交易，等待与MT4匹配"""
        try:
            mapping = TradeMapping(
                system_trade_id=trade_id,
                mt4_ticket=0,  # 待匹配
                symbol=trade_data['symbol'],
                action=trade_data['action'],
                volume=trade_data['volume'],
                system_entry_price=trade_data['entry_price'],
                mt4_entry_price=0.0,  # 待匹配
                created_at=datetime.now(),
                status="PENDING"
            )
            
            self.pending_mappings.append(mapping)
            self.logger.info(f"📝 注册系统交易等待匹配: {trade_id} {trade_data['symbol']} {trade_data['action']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"注册系统交易失败: {e}")
            return False
    
    def _sync_loop(self):
        """同步循环"""
        self.logger.info("交易结果同步循环启动")
        
        while self.running:
            try:
                # 尝试匹配待处理的交易
                self._match_pending_trades()
                
                # 清理过期的待匹配交易
                self._cleanup_expired_mappings()
                
                # 等待下次检查
                time.sleep(10)
                
            except Exception as e:
                self.logger.error(f"交易结果同步循环异常: {e}")
                time.sleep(10)
        
        self.logger.info("交易结果同步循环结束")
    
    def _match_pending_trades(self):
        """匹配待处理的交易"""
        try:
            if not self.pending_mappings:
                return
            
            # 获取最近的MT4订单
            recent_mt4_orders = self.mt4_tracker.get_recent_orders(hours=1)
            
            matched_mappings = []
            
            for mapping in self.pending_mappings:
                for mt4_order in recent_mt4_orders:
                    if self._is_trade_match(mapping, mt4_order):
                        # 找到匹配
                        mapping.mt4_ticket = mt4_order.ticket
                        mapping.mt4_entry_price = mt4_order.open_price
                        mapping.status = "MATCHED"
                        
                        # 保存映射关系
                        self.trade_mappings[mapping.system_trade_id] = mapping
                        matched_mappings.append(mapping)
                        
                        self.logger.info(f"✅ 交易匹配成功: {mapping.system_trade_id} ↔ MT4#{mt4_order.ticket}")
                        
                        # 如果MT4订单已经关闭，立即同步结果
                        if mt4_order.status == "CLOSED":
                            self._sync_trade_result(mapping, mt4_order)
                        
                        break
            
            # 移除已匹配的交易
            for mapping in matched_mappings:
                self.pending_mappings.remove(mapping)
                
        except Exception as e:
            self.logger.error(f"匹配待处理交易失败: {e}")
    
    def _is_trade_match(self, mapping: TradeMapping, mt4_order: MT4Order) -> bool:
        """检查交易是否匹配"""
        try:
            # 检查基本信息
            if (mapping.symbol != mt4_order.symbol or
                mapping.action != mt4_order.order_type or
                abs(mapping.volume - mt4_order.volume) > 0.001):
                return False
            
            # 检查价格容忍度
            price_diff = abs(mapping.system_entry_price - mt4_order.open_price)
            if price_diff > self.price_tolerance:
                return False
            
            # 检查时间容忍度
            time_diff = abs((mapping.created_at - mt4_order.open_time).total_seconds())
            if time_diff > self.time_tolerance:
                return False
            
            # 检查是否已经被匹配
            for existing_mapping in self.trade_mappings.values():
                if existing_mapping.mt4_ticket == mt4_order.ticket:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"检查交易匹配失败: {e}")
            return False
    
    def _cleanup_expired_mappings(self):
        """清理过期的映射"""
        try:
            current_time = datetime.now()
            expired_mappings = []
            
            for mapping in self.pending_mappings:
                if (current_time - mapping.created_at).total_seconds() > 600:  # 10分钟过期
                    expired_mappings.append(mapping)
            
            for mapping in expired_mappings:
                self.pending_mappings.remove(mapping)
                self.logger.warning(f"⏰ 交易匹配超时: {mapping.system_trade_id}")
                
        except Exception as e:
            self.logger.error(f"清理过期映射失败: {e}")
    
    def _on_mt4_order_opened(self, mt4_order: MT4Order):
        """MT4订单开仓回调"""
        try:
            self.logger.info(f"🔵 MT4订单开仓: #{mt4_order.ticket} {mt4_order.symbol} {mt4_order.order_type}")
            
            # 尝试立即匹配
            for mapping in self.pending_mappings:
                if self._is_trade_match(mapping, mt4_order):
                    mapping.mt4_ticket = mt4_order.ticket
                    mapping.mt4_entry_price = mt4_order.open_price
                    mapping.status = "MATCHED"
                    
                    self.trade_mappings[mapping.system_trade_id] = mapping
                    self.pending_mappings.remove(mapping)
                    
                    self.logger.info(f"✅ 即时匹配成功: {mapping.system_trade_id} ↔ MT4#{mt4_order.ticket}")
                    break
                    
        except Exception as e:
            self.logger.error(f"处理MT4订单开仓失败: {e}")
    
    def _on_mt4_order_closed(self, mt4_order: MT4Order):
        """MT4订单关闭回调"""
        try:
            self.logger.info(f"🔴 MT4订单关闭: #{mt4_order.ticket} 盈亏: ${mt4_order.profit:.2f}")
            
            # 查找对应的系统交易
            mapping = None
            for trade_mapping in self.trade_mappings.values():
                if trade_mapping.mt4_ticket == mt4_order.ticket:
                    mapping = trade_mapping
                    break
            
            if mapping:
                self._sync_trade_result(mapping, mt4_order)
            else:
                self.logger.warning(f"⚠️ 未找到MT4订单#{mt4_order.ticket}对应的系统交易")
                
        except Exception as e:
            self.logger.error(f"处理MT4订单关闭失败: {e}")
    
    def _sync_trade_result(self, mapping: TradeMapping, mt4_order: MT4Order):
        """同步交易结果到学习系统"""
        try:
            # 计算真实的交易结果
            exit_data = {
                'exit_price': mt4_order.close_price,
                'exit_reason': self._determine_exit_reason(mt4_order),
                'mt4_profit': mt4_order.profit,
                'mt4_commission': mt4_order.commission,
                'mt4_swap': mt4_order.swap,
                'mt4_ticket': mt4_order.ticket,
                'price_slippage': abs(mapping.system_entry_price - mt4_order.open_price),
                'execution_delay': (mt4_order.open_time - mapping.created_at).total_seconds()
            }
            
            # 同步到学习系统
            self.learning_coordinator.record_trade_exit(mapping.system_trade_id, exit_data)
            
            # 更新映射状态
            mapping.status = "CLOSED"
            
            self.logger.info(f"📊 交易结果同步完成: {mapping.system_trade_id} → MT4盈亏: ${mt4_order.profit:.2f}")
            
        except Exception as e:
            self.logger.error(f"同步交易结果失败: {e}")
    
    def _determine_exit_reason(self, mt4_order: MT4Order) -> str:
        """确定平仓原因"""
        try:
            # 检查是否触及止损
            if mt4_order.stop_loss > 0:
                if mt4_order.order_type == "BUY" and mt4_order.close_price <= mt4_order.stop_loss:
                    return "stop_loss"
                elif mt4_order.order_type == "SELL" and mt4_order.close_price >= mt4_order.stop_loss:
                    return "stop_loss"
            
            # 检查是否触及止盈
            if mt4_order.take_profit > 0:
                if mt4_order.order_type == "BUY" and mt4_order.close_price >= mt4_order.take_profit:
                    return "take_profit"
                elif mt4_order.order_type == "SELL" and mt4_order.close_price <= mt4_order.take_profit:
                    return "take_profit"
            
            # 检查注释中的信息
            comment = mt4_order.comment.lower()
            if 'sl' in comment or 'stop' in comment:
                return "stop_loss"
            elif 'tp' in comment or 'profit' in comment:
                return "take_profit"
            elif 'manual' in comment:
                return "manual"
            
            return "unknown"
            
        except Exception as e:
            self.logger.error(f"确定平仓原因失败: {e}")
            return "unknown"
    
    def get_sync_statistics(self) -> Dict:
        """获取同步统计"""
        try:
            total_mappings = len(self.trade_mappings)
            pending_mappings = len(self.pending_mappings)
            closed_mappings = len([m for m in self.trade_mappings.values() if m.status == "CLOSED"])
            
            return {
                'sync_enabled': self.sync_enabled,
                'total_mappings': total_mappings,
                'pending_mappings': pending_mappings,
                'matched_mappings': total_mappings - pending_mappings,
                'closed_mappings': closed_mappings,
                'success_rate': closed_mappings / max(total_mappings, 1)
            }
            
        except Exception as e:
            self.logger.error(f"获取同步统计失败: {e}")
            return {'error': str(e)}
    
    def force_sync_recent_trades(self, hours: int = 24):
        """强制同步最近的交易"""
        try:
            self.logger.info(f"🔄 强制同步最近{hours}小时的交易...")
            
            # 获取最近的MT4订单
            recent_orders = self.mt4_tracker.get_recent_orders(hours)
            
            synced_count = 0
            for mt4_order in recent_orders:
                if mt4_order.status == "CLOSED":
                    # 检查是否已经同步
                    already_synced = False
                    for mapping in self.trade_mappings.values():
                        if mapping.mt4_ticket == mt4_order.ticket:
                            already_synced = True
                            break
                    
                    if not already_synced:
                        # 创建临时映射并同步
                        temp_mapping = TradeMapping(
                            system_trade_id=f"mt4_sync_{mt4_order.ticket}",
                            mt4_ticket=mt4_order.ticket,
                            symbol=mt4_order.symbol,
                            action=mt4_order.order_type,
                            volume=mt4_order.volume,
                            system_entry_price=mt4_order.open_price,
                            mt4_entry_price=mt4_order.open_price,
                            created_at=mt4_order.open_time,
                            status="MATCHED"
                        )
                        
                        self._sync_trade_result(temp_mapping, mt4_order)
                        synced_count += 1
            
            self.logger.info(f"✅ 强制同步完成，同步了{synced_count}笔交易")
            
        except Exception as e:
            self.logger.error(f"强制同步失败: {e}")
    
    def enable_sync(self, enabled: bool = True):
        """启用/禁用同步"""
        self.sync_enabled = enabled
        self.logger.info(f"交易结果同步 {'启用' if enabled else '禁用'}")
    
    def is_sync_enabled(self) -> bool:
        """检查同步是否启用"""
        return self.sync_enabled
