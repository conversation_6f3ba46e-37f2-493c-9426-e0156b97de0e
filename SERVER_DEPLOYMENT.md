# QuantumForex Pro 服务器部署指南

## 🚀 快速部署步骤

### 1. 上传项目文件
将整个 `QuantumForex_Pro` 文件夹复制到服务器

### 2. 进入项目目录
```cmd
cd QuantumForex_Pro
```

### 3. 运行快速修复脚本（重要！）
**首先修复app.utils引用问题：**
```cmd
python quick_fix.py
```
选择 `y` 自动修复所有问题

### 4. 运行自动安装脚本
**方法一：使用批处理文件（推荐）**
```cmd
install.bat
```

**方法二：使用Python脚本**
```cmd
python install_dependencies.py
```

### 5. 启动系统
```cmd
python start.py
```

**预期看到的成功启动信息：**
```
🚀 QuantumForex Pro 启动检查...
✅ Python版本检查通过
✅ 所有依赖包检查通过
🎯 QuantumForex Pro 启动完成!
🔄 QuantumForex Pro 开始运行...
```

## 🔧 手动安装依赖（如果自动安装失败）

### 核心依赖（必须安装）
```cmd
pip install pandas>=1.3.0
pip install numpy>=1.21.0
pip install scikit-learn>=1.6.0
pip install PyMySQL>=1.0.2
pip install python-dotenv>=0.19.0
pip install requests>=2.25.0
pip install psutil>=7.0.0
pip install scipy>=1.7.0
pip install pyzmq>=24.0.0
pip install python-dateutil>=2.8.0
```

### 可选依赖
```cmd
pip install redis>=4.0.0
pip install ujson>=4.0.0
pip install colorlog>=6.0.0
pip install PyYAML>=6.0
pip install websocket-client>=1.2.0
```

## 🧪 测试安装

### 测试模块导入
```cmd
python -c "from utils.db_client import test_connection; print('✅ 数据库模块正常')"
python -c "from utils.mt4_client import MT4Client; print('✅ MT4客户端正常')"
python -c "from utils.intelligent_pair_selector import IntelligentPairSelector; print('✅ 智能选择器正常')"
```

### 测试系统启动
```cmd
python start.py
```

## ❗ 常见问题解决

### 问题1: "No module named 'app.utils'"
**原因**: 代码中仍有引用外部app模块的地方
**解决**: 运行 `python quick_fix.py` 自动修复

### 问题2: "No module named 'pandas'" 等依赖错误
**原因**: Python依赖包未安装
**解决**: 运行 `install.bat` 或手动安装依赖

### 问题3: "Permission denied" 权限错误
**原因**: 文件权限不足
**解决**: 以管理员身份运行命令提示符

### 问题4: pip安装失败
**原因**: 网络问题或pip版本过旧
**解决**:
```cmd
python -m pip install --upgrade pip
python -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org [包名]
```

### 问题5: 数据库连接失败
**原因**: 环境变量未配置或网络不通
**解决**: 检查 `.env` 文件配置

## 📁 项目结构确认

确保项目结构如下：
```
QuantumForex_Pro/
├── utils/                    # 工具模块（必须存在）
│   ├── __init__.py
│   ├── db_client.py
│   ├── mt4_client.py
│   └── intelligent_pair_selector.py
├── core/                     # 核心引擎
├── strategies/               # 交易策略
├── config/                   # 配置文件
├── data/                     # 数据存储
├── main.py                   # 主程序
├── start.py                  # 启动脚本
├── install.bat               # 安装脚本
├── install_dependencies.py   # Python安装脚本
├── quick_fix.py              # 快速修复脚本
└── requirements.txt          # 依赖列表
```

## 🎯 验证部署成功

当看到以下输出时，表示部署成功：
```
[2025-05-28 XX:XX:XX] 🚀 QuantumForex Pro 启动成功
[2025-05-28 XX:XX:XX] 📊 系统版本: v2.0.0
[2025-05-28 XX:XX:XX] 🔧 运行模式: 生产模式
[2025-05-28 XX:XX:XX] 💾 数据库: 连接正常
[2025-05-28 XX:XX:XX] 📡 MT4服务器: 连接正常
[2025-05-28 XX:XX:XX] 🧠 智能分析: 准备就绪
```

## 📞 技术支持

如果遇到其他问题，请检查：
1. Python版本是否为3.8+
2. 网络连接是否正常
3. 防火墙是否阻止了连接
4. 磁盘空间是否充足

---
**QuantumForex Pro - 世界顶级量化交易系统**
